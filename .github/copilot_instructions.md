# Project overview
This is an Laravel 12 project. Pls always try to use latest version and features of Laravel.

Tech stack:
- PHP 8.3
- Laravel 12
- Shadcn UI
- Tailwind CSS
- MySQL

This project is selling Nalan test packs (which include many exams) and Nalan pack tests is a product of Nalan company. The project is a web application that allows users to purchase and take these tests online.
This project is called "AussieHub" -> pls remember that.

## UI component
We write component as blade component

## Laravel
- We do not use $fillable property for model, we already setup Model::unguarded()
- <PERSON>way try to use typed system from PHP 8.3, don't write docblock unless necessary
- Never write $fillable and $guarded = [] in the Model
- Avoid using $fillable

## Validation
- Use array for validate rules
- If the rule is enum, use Enum rule

## Testing
- We use PEST php to write tests