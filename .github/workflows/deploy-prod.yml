name: Deploy production

on:
  push:
    branches: [ "main" ]

jobs:
  setup:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Deploy scripts
        uses: appleboy/ssh-action@master
        with:
          host: ************
          username: flashvps
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          script: |
            cd /home/<USER>/aussieeduhub.com.au
            git pull origin main
            composer install --no-dev --optimize-autoloader --no-ansi
            php artisan migrate --force
            php artisan optimize

            /home/<USER>/.local/share/pnpm/pnpm install && /home/<USER>/.local/share/pnpm/pnpm run build

            sudo -S service php8.3-fpm reload

            curl -s "https://ping2.me/@daudau/sweb-stuff?message=aussieeduhub.com.au%20deployed" > /dev/null