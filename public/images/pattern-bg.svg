<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
  <!-- Grid pattern -->
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255, 255, 255, 0.2)" stroke-width="1"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="400" fill="white"/>
  
  <!-- Grid overlay -->
  <rect width="400" height="400" fill="url(#grid)"/>
  
  <!-- Random circles -->
  <circle cx="50" cy="70" r="8" fill="none" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
  <circle cx="120" cy="160" r="12" fill="none" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
  <circle cx="200" cy="90" r="10" fill="none" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
  <circle cx="280" cy="220" r="15" fill="none" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
  <circle cx="350" cy="120" r="7" fill="none" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
  <circle cx="100" cy="300" r="9" fill="none" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
  <circle cx="230" cy="330" r="11" fill="none" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
  <circle cx="320" cy="350" r="8" fill="none" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
  <circle cx="180" cy="250" r="14" fill="none" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
  <circle cx="30" cy="180" r="10" fill="none" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
</svg>
