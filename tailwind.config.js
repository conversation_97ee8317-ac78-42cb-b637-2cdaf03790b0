import preset from './vendor/filament/support/tailwind.config.preset';
import colors from 'tailwindcss/colors';

/** @type {import('tailwindcss').Config} */
export default {
    presets: [preset],
    content: [
        './resources/**/*.blade.php',
        './vendor/filament/**/*.blade.php',
    ],
    theme: {
        extend: {
            colors: {
                primary: {
                    50: 'oklch(0.97 0.02 240 / <alpha-value>)',
                    100: 'oklch(0.91 0.05 240 / <alpha-value>)',
                    200: 'oklch(0.85 0.08 240 / <alpha-value>)',
                    300: 'oklch(0.79 0.11 240 / <alpha-value>)',
                    400: 'oklch(0.73 0.14 240 / <alpha-value>)',
                    500: 'oklch(0.67 0.17 240 / <alpha-value>)',
                    600: 'oklch(0.61 0.15 240 / <alpha-value>)',
                    700: 'oklch(0.55 0.13 240 / <alpha-value>)',
                    DEFAULT: 'oklch(0.65 0.2 240 / <alpha-value>)'
                },
                border: 'oklch(0.922 0 0 / <alpha-value>)',
                input: 'oklch(0.922 0 0 / <alpha-value>)',
                ring: 'oklch(0.87 0 0 / <alpha-value>)',
                background: 'oklch(1 0 0 / <alpha-value>)',
                'primary-foreground': 'oklch(0.985 0 0 / <alpha-value>)',
                foreground: {
                    DEFAULT: 'oklch(0.145 0 0 / <alpha-value>)'
                },
                gray: {
                    ...colors.gray
                }
            },
            fontFamily: {
                sans: ['instrument-sans', 'ui-sans-serif', 'system-ui', 'sans-serif']
            },
            borderRadius: {
                sm: '6px',
                lg: '8px',
            }
        }
    },
    plugins: []
};
