<?php

namespace App\Console\Commands;

use App\Naplan\TestPackRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-sitemap';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate sitemap.xml file for the website';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating sitemap...');

        try {
            $xml = $this->generateSitemapXml();

            // Save the sitemap to public directory
            $path = public_path('sitemap.xml');
            File::put($path, $xml);

            $this->info("Sitemap generated successfully at: {$path}");
            $this->info('Total URLs: ' . $this->countUrls($xml));

        } catch (\Exception $e) {
            $this->error('Failed to generate sitemap: ' . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    /**
     * Generate the sitemap XML content
     */
    private function generateSitemapXml(): string
    {
        $baseUrl = 'https://aussieeduhub.com.au';
        $now = now()->toISOString();

        // Start XML sitemap
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . PHP_EOL;

        // Add main pages
        $staticPages = [
            ['url' => '/', 'priority' => '1.0', 'changefreq' => 'daily'],
            ['url' => '/test-packs', 'priority' => '0.9', 'changefreq' => 'weekly'],
            ['url' => '/about', 'priority' => '0.6', 'changefreq' => 'monthly'],
            ['url' => '/contact', 'priority' => '0.6', 'changefreq' => 'monthly'],
            ['url' => '/privacy-policy', 'priority' => '0.4', 'changefreq' => 'yearly'],
            ['url' => '/terms-and-conditions', 'priority' => '0.4', 'changefreq' => 'yearly'],
            ['url' => '/faq', 'priority' => '0.7', 'changefreq' => 'monthly'],
        ];

        foreach ($staticPages as $page) {
            $xml .= $this->addUrlToSitemap(
                $baseUrl . $page['url'],
                $now,
                $page['changefreq'],
                $page['priority']
            );
        }

        // Add test pack pages
        $testPackRepository = app(TestPackRepository::class);
        $testPacks = $testPackRepository->allTestPacks();

        foreach ($testPacks as $testPack) {
            // Only include test packs that have tests available
            if ($testPack->hasTests()) {
                $xml .= $this->addUrlToSitemap(
                    $baseUrl . "/test-packs/{$testPack->Id}",
                    $now,
                    'weekly',
                    '0.8'
                );
            }
        }

        $xml .= '</urlset>' . PHP_EOL;

        return $xml;
    }

    /**
     * Add a URL entry to the sitemap
     */
    private function addUrlToSitemap(string $url, string $lastmod, string $changefreq, string $priority): string
    {
        $xml = '  <url>' . PHP_EOL;
        $xml .= '    <loc>' . htmlspecialchars($url, ENT_XML1) . '</loc>' . PHP_EOL;
        $xml .= '    <lastmod>' . $lastmod . '</lastmod>' . PHP_EOL;
        $xml .= '    <changefreq>' . $changefreq . '</changefreq>' . PHP_EOL;
        $xml .= '    <priority>' . $priority . '</priority>' . PHP_EOL;
        $xml .= '  </url>' . PHP_EOL;

        return $xml;
    }

    /**
     * Count the number of URLs in the sitemap
     */
    private function countUrls(string $xml): int
    {
        return substr_count($xml, '<url>');
    }
}
