<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;

class DashboardController extends Controller
{
    /**
     * Show the dashboard.
     */
    public function index(Request $request): View
    {
        $user = $request->user();

        // Calculate dashboard stats
        $totalTestPacks = $user->boughtTestPacks()->count();
        $totalResources = $user->boughtResources()->count();
        $totalStudents = $user->students()->count();

        // Get recent activities
        $recentActivities = $this->getRecentActivities($user);

        return view('dashboard.index', [
            'user' => $user,
            'totalTestPacks' => $totalTestPacks,
            'totalResources' => $totalResources,
            'totalStudents' => $totalStudents,
            'recentActivities' => $recentActivities,
        ]);
    }

    /**
     * Get recent activities for the dashboard
     */
    private function getRecentActivities($user)
    {
        $activities = collect();

        // Recent orders/purchases only
        $recentOrders = $user->orders()
            ->paid()
            ->latest('ordered_at')
            ->limit(5)
            ->get();

        foreach ($recentOrders as $order) {
            $activities->push([
                'type' => 'purchase',
                'icon' => 'currency-dollar',
                'icon_color' => 'purple',
                'title' => 'New purchase',
                'description' => $order->total(),
                'time' => $order->ordered_at->diffForHumans(),
                'created_at' => $order->ordered_at,
            ]);
        }

        return $activities;
    }
}
