<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\StudentResource;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;

class StudentAuthController extends Controller
{
    public function validateCredentials(Request $request): JsonResponse
    {
        $credentials = $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $student = Student::where('username', $credentials['username'])->first();

        if ($student && Hash::check($credentials['password'], $student->password)) {
            return response()->json([
                'student' => new StudentResource($student)
            ], 200);
        }

        return response()->json(['error' => 'Invalid credentials'], 401);
    }
}
