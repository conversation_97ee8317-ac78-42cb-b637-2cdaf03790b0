<?php

namespace App\Http\Controllers;

use App\Actions\GetAllPacks;
use App\Naplan\TestPackRepository;
use App\Services\BlogService;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function __invoke(TestPackRepository $testPackRepository, BlogService $blogService)
    {
        // Get latest 3 blog posts for the home page
        $blogData = $blogService->getPosts(10, 1);

        return view('home', [
            'testPacks' => $testPackRepository->allTestPacks(),
            'latestPosts' => $blogData['posts'] ?? [],
        ]);
    }
}
