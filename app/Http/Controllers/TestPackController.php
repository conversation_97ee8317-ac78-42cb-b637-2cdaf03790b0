<?php

namespace App\Http\Controllers;

use App\Actions\AssignTestPackToUser;
use App\Naplan\TestPack;
use App\Naplan\TestPackRepository;
use Illuminate\Http\Request;

class TestPackController extends Controller
{
    /**
     * Display a listing of all test packs with optional filtering.
     */
    public function index(TestPackRepository $testPackRepository)
    {
        $allTestPacks = collect($testPackRepository->allTestPacks());

        $categories = $allTestPacks
            ->mapWithKeys(fn(TestPack $testPack) => [$testPack->Category => $testPack->getCategoryLabel()])
            ->unique()
            ->toArray();

        $years = $allTestPacks->pluck('Level')->unique()->sort()->values();

        return view('test-packs.index', [
            'testPacks'  => $allTestPacks,
            'categories' => $categories,
            'years'      => $years,
        ]);
    }

    /**
     * Display the specified pack.
     */
    public function show(string $packId, TestPackRepository $testPackRepository)
    {
        $testPack = $testPackRepository->find($packId);

        // Get all test packs and filter out the current one
        $allTestPacks = collect($testPackRepository->allTestPacks())
            ->filter(function ($pack) use ($packId) {
                return $pack->Id != $packId;
            });

        // Get 3 random related test packs
        $relatedTestPacks = $allTestPacks->random(min(3, $allTestPacks->count()));

        return view('test-packs.show', [
            'testPack'         => $testPack,
            'relatedTestPacks' => $relatedTestPacks,
        ]);
    }

    /**
     * Process the pack checkout.
     */
    public function checkout(string $id, TestPackRepository $testPackRepository)
    {
        $testPack = $testPackRepository->find($id);

        // Prevent checkout if test pack has no tests
        if (!$testPack->hasTests()) {
            return redirect()->route('testPacks.show', $id)
                ->with('error', 'This test pack is not available for purchase yet. It\'s coming soon!');
        }

        $url = user()->checkoutTestPackUrl($testPack);

        return redirect()->away($url);
    }



    /**
     * Claim a free test pack.
     */
    public function claim(string $id, TestPackRepository $testPackRepository, AssignTestPackToUser $assignTestPackToUser)
    {
        $testPack = $testPackRepository->find($id);

        // Verify this is actually a free test pack
        if ($testPack->getDiscountedPrice() > 0) {
            return redirect()->route('testPacks.show', $id)
                ->with('error', 'This test pack is not free and requires payment.');
        }

        // Prevent claiming if test pack has no tests
        if (!$testPack->hasTests()) {
            return redirect()->route('testPacks.show', $id)
                ->with('error', 'This test pack is not available yet. It\'s coming soon!');
        }

        // Assign the test pack to the current user
        $assignTestPackToUser->handle((int) $testPack->Id, user());

        // Redirect to success page
        return redirect()->route('checkout.success', ['type' => 'test-pack', 'product_id' => $id])
            ->with('success', 'Test pack claimed successfully! You now have access to all tests in this pack.');
    }
}
