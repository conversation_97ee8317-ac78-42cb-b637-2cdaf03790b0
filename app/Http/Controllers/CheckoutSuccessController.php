<?php

namespace App\Http\Controllers;

use App\Naplan\TestPackRepository;
use App\Services\ResourcesService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class CheckoutSuccessController extends Controller
{
    public function __construct(
        protected TestPackRepository $testPackRepository,
        protected ResourcesService $resourcesService
    ) {}

    /**
     * Handle the unified checkout success callback
     */
    public function success(Request $request): View
    {
        $type = $request->query('type');
        $productId = $request->query('product_id');
        $orderId = $request->query('order_id');
        $email = $request->query('email');
        $total = $request->query('total');

        // Validate required parameters
        if (!$type || !$productId) {
            abort(400, 'Missing required parameters');
        }

        if ($type === 'test-pack') {
            return $this->handleTestPackSuccess($productId, $orderId, $email, $total);
        } elseif ($type === 'resource') {
            return $this->handleResourceSuccess($productId, $orderId, $email, $total);
        } else {
            abort(400, 'Invalid type parameter');
        }
    }

    /**
     * Handle test pack checkout success
     */
    private function handleTestPackSuccess(string $productId, ?string $orderId, ?string $email, ?string $total): View
    {
        $testPack = $this->testPackRepository->find($productId);

        return view('checkout.success', [
            'type' => 'test-pack',
            'product' => $testPack,
            'orderId' => $orderId,
            'email' => $email,
            'total' => $total,
        ]);
    }

    /**
     * Handle resource checkout success
     */
    private function handleResourceSuccess(string $productId, ?string $orderId, ?string $email, ?string $total): View
    {
        $products = $this->resourcesService->getDigitalProducts(1, 100);
        $resource = null;

        foreach ($products as $product) {
            if ($product['id'] == $productId) {
                $resource = $product;
                break;
            }
        }

        if (!$resource) {
            abort(404, 'Resource not found');
        }

        return view('checkout.success', [
            'type' => 'resource',
            'product' => $resource,
            'orderId' => $orderId,
            'email' => $email,
            'total' => $total,
        ]);
    }
}
