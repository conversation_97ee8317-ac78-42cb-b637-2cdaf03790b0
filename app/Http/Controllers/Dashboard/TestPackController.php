<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\BoughtTestPack;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\View\View;

class TestPackController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display a listing of the test packs.
     */
    public function index(): View
    {
        return view('dashboard.test-packs.index');
    }

    /**
     * Display the specified test pack.
     */
    public function show(BoughtTestPack $boughtTestPack): View
    {
        $this->authorize('view', $boughtTestPack);

        return view('dashboard.test-packs.show', [
            'testPackId' => $boughtTestPack->test_pack_id
        ]);
    }
}
