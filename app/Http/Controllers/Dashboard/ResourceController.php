<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\BoughtResource;
use App\Services\ResourcesService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ResourceController extends Controller
{
    use AuthorizesRequests;

    public function __construct(
        protected ResourcesService $resourcesService
    ) {}

    /**
     * Display a listing of the user's bought resources.
     */
    public function index(): View
    {
        return view('dashboard.resources.index');
    }

    /**
     * Display the specified resource with download links.
     */
    public function show(BoughtResource $boughtResource): View
    {
        $this->authorize('view', $boughtResource);

        // Get resource data
        $resourceData = $boughtResource->getResourceData();
        
        // Get download files
        $downloadFiles = $this->resourcesService->getFilesByProductId((int) $boughtResource->resource_id);

        return view('dashboard.resources.show', [
            'boughtResource' => $boughtResource,
            'resource' => $resourceData,
            'downloadFiles' => $downloadFiles,
        ]);
    }
}
