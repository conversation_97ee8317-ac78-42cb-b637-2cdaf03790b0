<?php

namespace App\Http\Controllers;

use App\Services\ResourcesService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ResourcesController extends Controller
{
    public function __construct(
        protected ResourcesService $resourcesService
    ) {}

    /**
     * Get all resources with pagination
     */
    public function index(Request $request): JsonResponse
    {
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 20);

        $files = $this->resourcesService->allFiles($page, $perPage);
        $totalCount = $this->resourcesService->getTotalFilesCount();

        return response()->json([
            'data' => $files,
            'meta' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $totalCount,
                'total_pages' => ceil($totalCount / $perPage)
            ]
        ]);
    }

    /**
     * Get resources for a specific product
     */
    public function productFiles(int $productId): JsonResponse
    {
        $files = $this->resourcesService->getFilesByProductId($productId);

        return response()->json([
            'data' => $files,
            'meta' => [
                'product_id' => $productId,
                'files_count' => count($files)
            ]
        ]);
    }

    /**
     * Get a specific resource
     */
    public function show(int $productId, string $downloadId): JsonResponse
    {
        $file = $this->resourcesService->getFileById($productId, $downloadId);

        if (!$file) {
            return response()->json([
                'message' => 'Resource not found'
            ], 404);
        }

        return response()->json([
            'data' => $file
        ]);
    }

    /**
     * Get all resources (products)
     */
    public function products(Request $request): JsonResponse
    {
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 20);

        $products = $this->resourcesService->getDigitalProducts($page, $perPage);
        $totalCount = $this->resourcesService->getTotalProductsCount();

        return response()->json([
            'data' => $products,
            'meta' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $totalCount,
                'total_pages' => ceil($totalCount / $perPage)
            ]
        ]);
    }

    /**
     * Get resource categories
     */
    public function categories(): JsonResponse
    {
        $categories = $this->resourcesService->getDigitalCategories();

        return response()->json([
            'data' => $categories,
            'meta' => [
                'categories_count' => count($categories)
            ]
        ]);
    }

    /**
     * Check if a resource exists
     */
    public function fileExists(int $productId, string $downloadId): JsonResponse
    {
        $exists = $this->resourcesService->fileExists($productId, $downloadId);

        return response()->json([
            'exists' => $exists,
            'product_id' => $productId,
            'download_id' => $downloadId
        ]);
    }

    /**
     * Get dashboard stats for resources
     */
    public function stats(): JsonResponse
    {
        $totalFiles = $this->resourcesService->getTotalFilesCount();
        $totalProducts = $this->resourcesService->getTotalProductsCount();
        $categories = $this->resourcesService->getDigitalCategories();

        return response()->json([
            'data' => [
                'total_files' => $totalFiles,
                'total_products' => $totalProducts,
                'total_categories' => count($categories),
                'avg_files_per_product' => $totalProducts > 0 ? round($totalFiles / $totalProducts, 2) : 0
            ]
        ]);
    }
}
