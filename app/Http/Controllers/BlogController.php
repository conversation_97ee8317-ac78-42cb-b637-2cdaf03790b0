<?php

namespace App\Http\Controllers;

use App\Services\BlogService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class BlogController extends Controller
{
    public function __construct(
        private BlogService $blogService
    ) {}

    /**
     * Display a listing of blog posts
     */
    public function index(Request $request): View
    {
        $page = (int) $request->get('page', 1);
        $perPage = 9; // Display 9 posts per page for nice grid layout

        $data = $this->blogService->getPosts($perPage, $page);

        return view('blog.index', [
            'posts' => $data, // Pass the full data structure
            'currentPage' => $page,
            'totalPages' => $data['total_pages'],
            'totalPosts' => $data['total_posts'],
            'hasNextPage' => $page < $data['total_pages'],
            'hasPrevPage' => $page > 1,
        ]);
    }

    /**
     * Display the specified blog post
     */
    public function show(string $slug): View
    {
        $post = $this->blogService->getPostBySlug($slug);

        if (!$post) {
            abort(404, 'Blog post not found');
        }

        // Get related posts (latest 3 posts excluding current one)
        $relatedPosts = collect($this->blogService->getPosts(4)['posts'])
            ->filter(fn($p) => $p['slug'] !== $slug)
            ->take(3)
            ->values()
            ->toArray();

        return view('blog.show', [
            'post' => $post,
            'relatedPosts' => $relatedPosts,
        ]);
    }
}
