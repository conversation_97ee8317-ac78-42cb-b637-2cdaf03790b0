<?php

namespace App\Http\Controllers;

use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Inertia\Inertia;

class StudentController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display a listing of the students.
     */
    public function index()
    {
        $students = auth()->user()->students()->latest()->get();

        return Inertia::render('students/index', [
            'students' => $students,
        ]);
    }

    /**
     * Store a newly created student in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:64|unique:students',
            'password' => 'required|string|min:4|max:64',
        ]);

        // Hash the password before storing
        $validated['password'] = Hash::make($validated['password']);

        $student = auth()->user()->students()->create($validated);

        return redirect()->route('students.index')
            ->with('message', 'Student created successfully.');
    }

    /**
     * Update the specified student in storage.
     */
    public function update(Request $request, Student $student)
    {
        $this->authorize('update', $student);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:64|unique:students,username,' . $student->id,
            'password' => 'nullable|string|min:4|max:64',
        ]);

        // Only hash the password if it was provided
        if (isset($validated['password']) && !empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            // If password is not provided, remove it from the update
            unset($validated['password']);
        }

        $student->update($validated);

        return redirect()->route('students.index')
            ->with('message', 'Student updated successfully.');
    }

    /**
     * Remove the specified student from storage.
     */
    public function destroy(Student $student)
    {
        $this->authorize('delete', $student);

        $student->delete();

        return redirect()->route('students.index')
            ->with('message', 'Student deleted successfully.');
    }
}