<?php

namespace App\Http\Controllers;

use App\Actions\AssignResourceToUser;
use App\Services\ResourcesService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ResourcesPageController extends Controller
{
    public function __construct(
        protected ResourcesService $resourcesService
    ) {}

    /**
     * Display a listing of resources
     */
    public function index(Request $request): View
    {
        $resources = $this->resourcesService->getAllDigitalProducts();

        return view('resources.index', [
            'resources' => $resources
        ]);
    }

    /**
     * Display the specified resource
     */
    public function show(int $id): View
    {
        // Get the actual product data
        $products = $this->resourcesService->getDigitalProducts(1, 100);
        $product = null;

        foreach ($products as $productItem) {
            if ($productItem['id'] == $id) {
                $product = $productItem;
                break;
            }
        }

        if (!$product) {
            abort(404, 'Resource not found');
        }

        // Get the files for this product
        $files = $this->resourcesService->getFilesByProductId($id);

        return view('resources.show', [
            'product' => $product,
            'files' => $files
        ]);
    }

    /**
     * Process the resource checkout
     */
    public function checkout(int $id)
    {
        $products = $this->resourcesService->getDigitalProducts(1, 100);
        $resource = null;

        foreach ($products as $product) {
            if ($product['id'] == $id) {
                $resource = $product;
                break;
            }
        }

        if (!$resource) {
            return redirect()->route('resources.show', $id)
                ->with('error', 'Resource not found.');
        }

        $price = $resource['sale_price'] ?: $resource['regular_price'];

        if ($price == 0) {
            return redirect()->route('resources.claim', $id);
        }

        $url = user()->checkoutResourceUrl($resource);
        return redirect()->away($url);
    }



    /**
     * Claim a free resource
     */
    public function claim(int $id, AssignResourceToUser $assignResourceToUser)
    {
        $products = $this->resourcesService->getDigitalProducts(1, 100);
        $resource = null;

        foreach ($products as $product) {
            if ($product['id'] == $id) {
                $resource = $product;
                break;
            }
        }

        if (!$resource) {
            return redirect()->route('resources.show', $id)
                ->with('error', 'Resource not found.');
        }

        $price = $resource['sale_price'] ?: $resource['regular_price'];

        if ($price > 0) {
            return redirect()->route('resources.show', $id)
                ->with('error', 'This resource is not free and requires payment.');
        }

        // Assign the resource to the current user
        $assignResourceToUser->handle((string) $id, user());

        // Redirect to success page
        return redirect()->route('checkout.success', ['type' => 'resource', 'product_id' => $id])
            ->with('success', 'Resource claimed successfully! You now have access to download this resource.');
    }
}
