<?php

namespace App\Providers;

use App\Listeners\OrderCreatedListener;
use App\Naplan\Backend;
use App\Services\DiscountService;
use App\Services\Woocommerce;
use App\Services\ResourcesService;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Illuminate\Validation\Rules\Password;
use LemonSqueezy\Laravel\Events\OrderCreated;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(Backend::class, function () {
            return new Backend(
                url: config('services.backend.url'),
                token: config('services.backend.token')
            );
        });

        $this->app->singleton(DiscountService::class, function () {
            return new DiscountService();
        });

        $this->app->singleton(Woocommerce::class, function () {
            return new Woocommerce(
                apiUrl: config('services.woocommere.url'),
                apiKey: config('services.woocommere.key'),
                apiSecret: config('services.woocommere.secret')
            );
        });

        $this->app->singleton(ResourcesService::class, function () {
            return new ResourcesService(
                $this->app->make(Woocommerce::class)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        URL::forceHttps(App::isProduction());

        $this->configureModels();
        $this->configurePassword();
        $this->configureFilament();
        $this->configureEvents();
    }

    protected function configureEvents(): void
    {
        Event::listen(OrderCreated::class, OrderCreatedListener::class);
    }

    protected function configureFilament(): void
    {
        Table::macro('minimal', function () {
            /** @var Table $this */
            return $this
                ->searchable(false)
                ->paginated(false);
        });
    }

    protected function configurePassword(): void
    {
        Password::defaults(fn() => Password::min(6));
    }

    protected function configureModels(): void
    {
        Model::unguard();
    }
}
