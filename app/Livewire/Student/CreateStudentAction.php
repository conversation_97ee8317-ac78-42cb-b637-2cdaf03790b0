<?php

namespace App\Livewire\Student;

use App\Forms\Components\AvatarSelector;
use App\Models\Student;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Livewire\Component;

class CreateStudentAction extends Component implements HasForms, HasActions
{
    use InteractsWithForms;
    use InteractsWithActions;

    public function createAction(): Action
    {
        return Action::make('create')
            ->label('Add Student')
            ->icon('heroicon-o-plus')
            ->color('primary')
            ->form([
                TextInput::make('name')
                    ->label('Name')
                    ->required()
                    ->maxLength(255),

                TextInput::make('username')
                    ->label('Username')
                    ->required()
                    ->unique(Student::class, 'username')
                    ->maxLength(64),

                TextInput::make('password')
                    ->label('Password')
                    ->required()
                    ->minLength(4)
                    ->maxLength(255),

                AvatarSelector::make('avatar')
                    ->label('Choose an Avatar')
                    ->default('avatar-1.png')
            ])
            ->action(function (array $data) {
                user()->students()->create($data);

                Notification::make()
                    ->title('Student created successfully')
                    ->success()
                    ->send();

                // Reload the page to refresh the students list
                return redirect()->route('dashboard.students.index');
            });
    }

    public function render()
    {
        return view('livewire.student.create-student-action');
    }
}
