<?php

namespace App\Livewire\Student;

use App\Forms\Components\AvatarSelector;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;

class StudentSettingsForm extends Component implements HasForms, HasActions
{
    use InteractsWithForms;
    use InteractsWithActions;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'name' => student()->name,
            'avatar' => student()->avatar,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->label('Name')
                    ->required()
                    ->maxLength(255),

                AvatarSelector::make('avatar')
                    ->label('Choose an Avatar'),

                TextInput::make('password')
                    ->label('New Password')
                    ->password()
                    ->minLength(4)
                    ->maxLength(255),

                TextInput::make('password_confirmation')
                    ->label('Confirm Password')
                    ->password()
                    ->dehydrated(false)
                    ->requiredWith('password')
                    ->same('password'),

                Actions::make([
                    Actions\Action::make('Save changes')
                        ->extraAttributes(['type' => 'submit'])
                ])
            ])
            ->statePath('data');
    }

    public function submit(): void
    {
        $data = $this->form->getState();

        if (blank($data['password'])) {
            unset($data['password']);
        }

        // Update student data
        student()->update($data);

        Notification::make()
            ->title('Awesome! Your profile has been updated')
            ->body('Your new look is ready to go!')
            ->success()
            ->icon('heroicon-o-sparkles')
            ->send();

        // Reset the password field
        $this->form->fill([
            'name' => student()->name,
            'avatar' => student()->avatar,
            'password' => '',
            'password_confirmation' => '',
        ]);
    }

    public function render()
    {
        return view('livewire.student.student-settings-form');
    }
}
