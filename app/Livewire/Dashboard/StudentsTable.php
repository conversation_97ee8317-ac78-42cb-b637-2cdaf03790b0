<?php

namespace App\Livewire\Dashboard;

use App\Models\Student;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Livewire\Component;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables;
use App\Forms\Components\AvatarSelector;

class StudentsTable extends Component implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;

    public function table(Table $table): Table
    {
        return $table
            ->minimal()
            ->query(function (): Builder {
                return Student::query()->where('user_id', auth()->id());
            })
            ->columns([
                ImageColumn::make('avatar_url')
                    ->label('Avatar')
                    ->circular()
                    ->size(40),
                TextColumn::make('name')
                    ->searchable(),
                TextColumn::make('username')
                    ->searchable(),
            ])
            ->defaultSort('id', 'desc')
            ->actions([
                Tables\Actions\EditAction::make()
                    ->form([
                        TextInput::make('name'),
                        TextInput::make('password'),
                        AvatarSelector::make('avatar'),
                    ])
                ->mutateFormDataUsing(function (array $data) {
                    if (blank($data['password'])) {
                        unset($data['password']);
                    }

                    return $data;
                })
                ,
                DeleteAction::make(),
            ]);
    }

    public function render()
    {
        return view('livewire.dashboard.students-table');
    }
}
