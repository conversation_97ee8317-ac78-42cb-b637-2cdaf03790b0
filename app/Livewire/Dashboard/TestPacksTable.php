<?php

namespace App\Livewire\Dashboard;

use App\Actions\AssignTestPackToStudents;
use App\Models\AssignedTestPack;
use App\Models\BoughtTestPack;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Livewire\Component;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\ViewAction;
use Filament\Forms\Components\CheckboxList;
use Filament\Tables\Actions\Action;

class TestPacksTable extends Component implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;

    public function table(Table $table): Table
    {
        return $table
            ->minimal()
            ->query(user()->boughtTestPacks()->getQuery()->latest())
            ->columns([
                TextColumn::make('no')
                    ->label('No.')
                    ->state(fn ($rowLoop) => $rowLoop->index + 1)
                    ->grow(false),
                TextColumn::make('title'),
                TextColumn::make('category')
                    ->formatStateUsing(fn (BoughtTestPack $record) => $record->testPack()->getCategoryLabel()),
                TextColumn::make('level'),
                TextColumn::make('students.name')
                    ->listWithLineBreaks(),
                TextColumn::make('expires_at')->date(),
            ])
            ->actions([
                Action::make('view')
                    ->icon('heroicon-o-eye')
                    ->label('View')
                    ->url(fn(BoughtTestPack $record) => route('dashboard.test-packs.show', $record)),
                Action::make('assign-students')
                    ->icon('heroicon-o-user-plus')
                    ->label('Assign')
                    ->tooltip('Assign students')
                    ->fillForm(function (array $data, BoughtTestPack $record) {
                        $data['student_ids'] = $record->students->pluck('id')->toArray();

                        return $data;
                    })
                    ->form([
                        CheckboxList::make('student_ids')
                            ->options(
                                user()->students()->pluck('name', 'id')->toArray()
                            )
                            ->columns(3)
                    ])
                    ->action(function (array $data, BoughtTestPack $record) {
                        AssignTestPackToStudents::make()->handle($record, $data['student_ids']);

                        Notification::make()
                            ->body('The students have been assigned to the test pack.')
                            ->success()
                            ->send();
                    })
            ]);
    }

    public function render()
    {
        return view('livewire.dashboard.test-packs-table');
    }
}
