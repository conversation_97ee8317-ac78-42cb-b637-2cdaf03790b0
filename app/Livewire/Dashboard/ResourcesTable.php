<?php

namespace App\Livewire\Dashboard;

use App\Models\BoughtResource;
use App\Services\ResourcesService;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Livewire\Component;

class ResourcesTable extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    protected ResourcesService $resourcesService;

    public function boot(ResourcesService $resourcesService)
    {
        $this->resourcesService = $resourcesService;
    }

    public function table(Table $table): Table
    {
        return $table
            ->minimal()
            ->query(user()->boughtResources()->getQuery()->latest())
            ->columns([
                TextColumn::make('no')
                    ->label('No.')
                    ->state(fn ($rowLoop) => $rowLoop->index + 1)
                    ->grow(false),
                TextColumn::make('resource_name')
                    ->label('Resource Name')
                    ->state(function (BoughtResource $record) {
                        $resourceData = $record->getResourceData();
                        return $resourceData['name'] ?? 'Unknown Resource';
                    }),
                TextColumn::make('resource_type')
                    ->label('Type')
                    ->state(function (BoughtResource $record) {
                        return 'Digital Resource';
                    }),
                TextColumn::make('files_count')
                    ->label('Files')
                    ->state(function (BoughtResource $record) {
                        $files = $this->resourcesService->getFilesByProductId((int) $record->resource_id);
                        return count($files) . ' ' . str('file')->plural(count($files));
                    }),
                TextColumn::make('created_at')
                    ->label('Purchased')
                    ->date(),
            ])
            ->actions([
                Action::make('download')
                    ->label('Download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('primary')
                    ->url(function (BoughtResource $record) {
                        return route('dashboard.resources.show', $record);
                    }),
            ])
            ->emptyStateHeading('No resources purchased yet')
            ->emptyStateDescription('Purchase resources from our shop to see them here.')
            ->emptyStateActions([
                Action::make('browse_resources')
                    ->label('Browse Resources')
                    ->icon('heroicon-o-shopping-cart')
                    ->color('primary')
                    ->url(route('resources.index')),
            ]);
    }

    public function render()
    {
        return view('livewire.dashboard.resources-table');
    }
}
