<?php

namespace App\Livewire\Dashboard;

use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Livewire\Component;

class UserSettingsForm extends Component implements HasForms, HasActions
{
    use InteractsWithForms;
    use InteractsWithActions;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'name' => user()->name,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->label('Name')
                    ->required()
                    ->maxLength(255),

                TextInput::make('password')
                    ->label('New Password')
                    ->password()
                    ->minLength(6)
                    ->maxLength(255),

                TextInput::make('password_confirmation')
                    ->label('Confirm Password')
                    ->password()
                    ->dehydrated(false)
                    ->requiredWith('password')
                    ->same('password'),

                Actions::make([
                    Actions\Action::make('Save changes')
                        ->extraAttributes(['type' => 'submit'])
                ])
            ])
            ->statePath('data');
    }

    public function submit(): void
    {
        $data = $this->form->getState();

        if (blank($data['password'])) {
            unset($data['password']);
        }

        // Update user data
        user()->update($data);

        Notification::make()
            ->title('Settings updated successfully')
            ->success()
            ->send();

        // Reset the password field
        $this->form->fill([
            'name'                  => user()->name,
            'password'              => '',
            'password_confirmation' => '',
        ]);
    }

    public function render()
    {
        return view('livewire.dashboard.user-settings-form');
    }
}
