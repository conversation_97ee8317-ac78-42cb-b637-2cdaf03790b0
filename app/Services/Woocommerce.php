<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class Woocommerce
{
    public function __construct(
        protected string $apiUrl,
        protected string $apiKey,
        protected string $apiSecret,
    ) {}

    public function request(string $method, string $endpoint, array $payload = []): array
    {
        $endpoint = trim($endpoint, '/');
        return Http::withBasicAuth($this->apiKey, $this->apiSecret)
            ->$method("{$this->apiUrl}/wp-json/wc/v3/{$endpoint}", $payload)
            ->throw()
            ->json();
    }
}
