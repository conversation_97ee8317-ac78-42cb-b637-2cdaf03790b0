<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;

/**
 * ResourcesService - Handles WooCommerce digital products/resources
 *
 * Caching Strategy:
 * - Uses Cache::flexible for intelligent cache management
 * - Cache keys: resources.{method}.{params}
 * - TTL: 1 minute to ~3.5 days (flexible based on usage)
 * - Cached methods: getAllDigitalProducts, getDigitalProducts, getFilesByProductId, getDigitalCategories
 */
class ResourcesService
{
    protected $digitalCategoryId = 'digital';
    protected $digitalCategoryName = 'digital';

    /**
     * Cache TTL configuration for flexible caching
     * [min_ttl, max_ttl] in seconds
     */
    public array $flexibleCacheTtl = [60, 300000]; // 1 minute to ~3.5 days

    public function __construct(
        protected Woocommerce $woocommerce
    ) {}

    /**
     * Get the digital category ID from WooCommerce
     */
    protected function getDigitalCategoryId(): ?int
    {
        try {
            $categories = $this->woocommerce->request('GET', 'products/categories', [
                'search' => $this->digitalCategoryName,
                'per_page' => 10
            ]);

            foreach ($categories as $category) {
                if (strtolower($category['name']) === strtolower($this->digitalCategoryName) ||
                    strtolower($category['slug']) === strtolower($this->digitalCategoryName)) {
                    return $category['id'];
                }
            }

            return null;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to get digital category ID: ' . $e->getMessage());
            return null;
        }
    }

    public function allFiles(int $page = 1, int $perPage = 100): array
    {
        try {
            $categoryId = $this->getDigitalCategoryId();

            $params = [
                'type' => 'simple',
                'status' => 'publish',
                'page' => $page,
                'per_page' => $perPage,
                'meta_key' => '_downloadable',
                'meta_value' => 'yes'
            ];

            // Add category filter if we found the digital category
            if ($categoryId) {
                $params['category'] = $categoryId;
            }

            $products = $this->woocommerce->request('GET', 'products', $params);

            $digitalFiles = [];

            foreach ($products as $product) {
                // Check if product is downloadable (digital)
                if (!isset($product['downloadable']) || !$product['downloadable']) {
                    continue;
                }

                // Extract download files
                $downloads = $product['downloads'] ?? [];

                foreach ($downloads as $download) {
                    $digitalFiles[] = [
                        'id' => $download['id'],
                        'name' => $download['name'],
                        'file' => $download['file'],
                        'product_id' => $product['id'],
                        'product_name' => $product['name'],
                        'product_slug' => $product['slug'],
                        'product_price' => $product['price'],
                        'product_sale_price' => $product['sale_price'],
                        'product_regular_price' => $product['regular_price'],
                        'product_description' => $product['short_description'],
                        'product_image' => $product['images'][0]['src'] ?? null,
                        'created_at' => $product['date_created'],
                        'modified_at' => $product['date_modified'],
                    ];
                }
            }

            return $digitalFiles;
        } catch (\Exception $e) {
            // Log error and return empty array on failure
            \Illuminate\Support\Facades\Log::error('Failed to fetch digital files from WooCommerce: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get digital files for a specific product (cached)
     */
    public function getFilesByProductId(int $productId): array
    {
        return Cache::flexible("resources.product_files.{$productId}", $this->flexibleCacheTtl, function () use ($productId) {
            return $this->fetchProductFilesFromApi($productId);
        });
    }

    /**
     * Fetch digital files for a specific product from WooCommerce API (uncached)
     */
    protected function fetchProductFilesFromApi(int $productId): array
    {
        try {
            $product = $this->woocommerce->request('GET', "products/{$productId}");

            // Check if product is downloadable (digital)
            if (!isset($product['downloadable']) || !$product['downloadable']) {
                return [];
            }

            $digitalFiles = [];
            $downloads = $product['downloads'] ?? [];

            foreach ($downloads as $download) {
                $digitalFiles[] = [
                    'id' => $download['id'],
                    'name' => $download['name'],
                    'file' => $download['file'],
                    'product_id' => $product['id'],
                    'product_name' => $product['name'],
                    'product_slug' => $product['slug'],
                    'product_price' => $product['price'],
                    'product_sale_price' => $product['sale_price'],
                    'product_regular_price' => $product['regular_price'],
                    'product_description' => $product['short_description'],
                    'product_image' => $product['images'][0]['src'] ?? null,
                    'created_at' => $product['date_created'],
                    'modified_at' => $product['date_modified'],
                ];
            }

            return $digitalFiles;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Failed to fetch digital files for product {$productId}: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get a specific digital file by download ID and product ID
     */
    public function getFileById(int $productId, string $downloadId): ?array
    {
        try {
            $product = $this->woocommerce->request('GET', "products/{$productId}");

            if (!isset($product['downloadable']) || !$product['downloadable']) {
                return null;
            }

            $downloads = $product['downloads'] ?? [];

            foreach ($downloads as $download) {
                if ($download['id'] === $downloadId) {
                    return [
                        'id' => $download['id'],
                        'name' => $download['name'],
                        'file' => $download['file'],
                        'product_id' => $product['id'],
                        'product_name' => $product['name'],
                        'product_slug' => $product['slug'],
                        'product_price' => $product['price'],
                        'product_sale_price' => $product['sale_price'],
                        'product_regular_price' => $product['regular_price'],
                        'product_description' => $product['short_description'],
                        'product_image' => $product['images'][0]['src'] ?? null,
                        'created_at' => $product['date_created'],
                        'modified_at' => $product['date_modified'],
                    ];
                }
            }

            return null;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Failed to get digital file {$downloadId} for product {$productId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get digital products (without file details) - cached
     */
    public function getDigitalProducts(int $page = 1, int $perPage = 100): array
    {
        $cacheKey = "resources.digital_products.page_{$page}.per_page_{$perPage}";

        return Cache::flexible($cacheKey, $this->flexibleCacheTtl, function () use ($page, $perPage) {
            return $this->fetchDigitalProductsFromApi($page, $perPage);
        });
    }

    /**
     * Fetch digital products from WooCommerce API (uncached)
     */
    protected function fetchDigitalProductsFromApi(int $page = 1, int $perPage = 100): array
    {
        try {
            $categoryId = $this->getDigitalCategoryId();

            $params = [
                'type' => 'simple',
                'status' => 'publish',
                'page' => $page,
                'per_page' => $perPage,
                'meta_key' => '_downloadable',
                'meta_value' => 'yes'
            ];

            // Add category filter if we found the digital category
            if ($categoryId) {
                $params['category'] = $categoryId;
            }

            $products = $this->woocommerce->request('GET', 'products', $params);

            $digitalProducts = [];

            foreach ($products as $product) {
                if (!isset($product['downloadable']) || !$product['downloadable']) {
                    continue;
                }

                $digitalProducts[] = [
                    'id' => $product['id'],
                    'name' => $product['name'],
                    'slug' => $product['slug'],
                    'price' => $product['price'],
                    'sale_price' => $product['sale_price'],
                    'regular_price' => $product['regular_price'],
                    'description' => $product['description'],
                    'short_description' => $product['short_description'],
                    'image' => $product['images'][0]['src'] ?? null,
                    'gallery' => array_map(fn($img) => $img['src'], $product['images']),
                    'download_count' => count($product['downloads'] ?? []),
                    'downloads' => $product['downloads'] ?? [],
                    'created_at' => $product['date_created'],
                    'modified_at' => $product['date_modified'],
                    'status' => $product['status'],
                    'featured' => $product['featured'],
                ];
            }

            return $digitalProducts;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to fetch digital products from WooCommerce: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all digital products without pagination (cached)
     */
    public function getAllDigitalProducts(): array
    {
        return Cache::flexible('resources.all_digital_products', $this->flexibleCacheTtl, function () {
            return $this->getDigitalProducts(1, 100); // Get first 100 products (should be enough for most cases)
        });
    }

    /**
     * Get total count of digital files
     */
    public function getTotalFilesCount(): int
    {
        try {
            $categoryId = $this->getDigitalCategoryId();

            $params = [
                'type' => 'simple',
                'status' => 'publish',
                'per_page' => 1, // We only need the count
                'meta_key' => '_downloadable',
                'meta_value' => 'yes'
            ];

            // Add category filter if we found the digital category
            if ($categoryId) {
                $params['category'] = $categoryId;
            }

            $response = $this->woocommerce->request('GET', 'products', $params);

            // WooCommerce returns total count in headers, but since we can't access headers
            // we'll count all products manually for now
            $allProducts = $this->woocommerce->request('GET', 'products', array_merge($params, ['per_page' => 100]));

            $totalFiles = 0;
            foreach ($allProducts as $product) {
                if (isset($product['downloadable']) && $product['downloadable']) {
                    $totalFiles += count($product['downloads'] ?? []);
                }
            }

            return $totalFiles;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to get total digital files count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get total count of digital products
     */
    public function getTotalProductsCount(): int
    {
        try {
            $categoryId = $this->getDigitalCategoryId();

            $params = [
                'type' => 'simple',
                'status' => 'publish',
                'per_page' => 100, // Get all products to count
                'meta_key' => '_downloadable',
                'meta_value' => 'yes'
            ];

            // Add category filter if we found the digital category
            if ($categoryId) {
                $params['category'] = $categoryId;
            }

            $products = $this->woocommerce->request('GET', 'products', $params);

            $count = 0;
            foreach ($products as $product) {
                if (isset($product['downloadable']) && $product['downloadable']) {
                    $count++;
                }
            }

            return $count;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to get total digital products count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Check if a file exists and is accessible
     */
    public function fileExists(int $productId, string $downloadId): bool
    {
        $file = $this->getFileById($productId, $downloadId);
        return $file !== null;
    }

    /**
     * Clear all resources-related cache
     */
    public function clearCache(): void
    {
        // Clear all resources cache keys
        Cache::forget('resources.all_digital_products');
        Cache::forget('resources.digital_categories');

        // Clear paginated products cache (common pages)
        for ($page = 1; $page <= 10; $page++) {
            for ($perPage = 10; $perPage <= 100; $perPage += 10) {
                Cache::forget("resources.digital_products.page_{$page}.per_page_{$perPage}");
            }
        }

        // Note: Product-specific caches (resources.product_files.{id}) would need
        // specific product IDs to clear, or could be cleared with cache tags if using Redis
    }

    /**
     * Clear cache for a specific product
     */
    public function clearProductCache(int $productId): void
    {
        Cache::forget("resources.product_files.{$productId}");
    }

    /**
     * Get all available categories that contain digital products (cached)
     */
    public function getDigitalCategories(): array
    {
        return Cache::flexible('resources.digital_categories', $this->flexibleCacheTtl, function () {
            return $this->fetchDigitalCategoriesFromApi();
        });
    }

    /**
     * Fetch digital categories from WooCommerce API (uncached)
     */
    protected function fetchDigitalCategoriesFromApi(): array
    {
        try {
            // Get all categories
            $categories = $this->woocommerce->request('GET', 'products/categories', [
                'per_page' => 100,
                'hide_empty' => true
            ]);

            $digitalCategories = [];

            foreach ($categories as $category) {
                // Check if this category has digital products
                $products = $this->woocommerce->request('GET', 'products', [
                    'category' => $category['id'],
                    'type' => 'simple',
                    'status' => 'publish',
                    'per_page' => 1,
                    'meta_key' => '_downloadable',
                    'meta_value' => 'yes'
                ]);

                if (!empty($products)) {
                    $digitalCategories[] = [
                        'id' => $category['id'],
                        'name' => $category['name'],
                        'slug' => $category['slug'],
                        'description' => $category['description'],
                        'count' => $category['count'],
                    ];
                }
            }

            return $digitalCategories;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to get digital categories: ' . $e->getMessage());
            return [];
        }
    }
}
