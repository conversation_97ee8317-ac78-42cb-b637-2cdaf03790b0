<?php

namespace App\Services;

use Carbon\Carbon;

class DiscountService
{
    /**
     * Check if there's an active discount
     */
    public function hasActiveDiscount(): bool
    {
        $discountConfig = config('aussie.discount');

        if (!$discountConfig['percent'] || $discountConfig['percent'] <= 0) {
            return false;
        }

        $now = now();

        // Check start date
        if ($discountConfig['start_date']) {
            $startDate = Carbon::parse($discountConfig['start_date'])->startOfDay();
            if ($now->lt($startDate)) {
                return false;
            }
        }

        // Check end date
        if ($discountConfig['end_date']) {
            $endDate = Carbon::parse($discountConfig['end_date'])->endOfDay();
            if ($now->gt($endDate)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the current discount percentage
     */
    public function getDiscountPercent(): int
    {
        if (!$this->hasActiveDiscount()) {
            return 0;
        }

        return config('aussie.discount.percent');
    }

    /**
     * Calculate the discounted price for a given original price
     */
    public function calculateDiscountedPrice(float $originalPrice): float
    {
        if (!$this->hasActiveDiscount()) {
            return $originalPrice;
        }

        $discountPercent = $this->getDiscountPercent();
        $discountAmount = $originalPrice * ($discountPercent / 100);

        return round($originalPrice - $discountAmount, 2);
    }

    /**
     * Calculate the savings amount for a given original price
     */
    public function calculateSavingsAmount(float $originalPrice): float
    {
        if (!$this->hasActiveDiscount()) {
            return 0;
        }

        return round($originalPrice - $this->calculateDiscountedPrice($originalPrice), 2);
    }

    /**
     * Get discount information for display purposes
     */
    public function getDiscountInfo(): array
    {
        return [
            'is_active' => $this->hasActiveDiscount(),
            'percent' => $this->getDiscountPercent(),
            'start_date' => config('aussie.discount.start_date'),
            'end_date' => config('aussie.discount.end_date'),
        ];
    }
}