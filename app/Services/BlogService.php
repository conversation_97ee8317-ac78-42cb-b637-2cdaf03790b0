<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class BlogService
{
    private string $wordpressUrl;

    public function __construct()
    {
        $this->wordpressUrl = 'https://cms.aussieeduhub.com.au';
    }

    /**
     * Get blog posts from WordPress API
     */
    public function getPosts(int $perPage = 10, int $page = 1): array
    {
        $cacheKey = "blog_posts_{$perPage}_{$page}";

        return Cache::remember($cacheKey, 300, function () use ($perPage, $page) {
            try {
                $response = Http::timeout(10)->get("{$this->wordpressUrl}/wp-json/wp/v2/posts", [
                    'per_page' => $perPage,
                    'page' => $page,
                    '_embed' => true, // Include featured images and author info
                    'status' => 'publish',
                ]);

                if ($response->successful()) {
                    $posts = $response->json();
                    return [
                        'posts' => $this->formatPosts($posts),
                        'total_pages' => (int) $response->header('X-WP-TotalPages', 1),
                        'total_posts' => (int) $response->header('X-WP-Total', 0),
                    ];
                }

                return ['posts' => [], 'total_pages' => 0, 'total_posts' => 0];
            } catch (\Exception $e) {
                Log::error('Failed to fetch blog posts: ' . $e->getMessage());
                return ['posts' => [], 'total_pages' => 0, 'total_posts' => 0];
            }
        });
    }    /**
     * Get single blog post by slug
     */
    public function getPostBySlug(string $slug): ?array
    {
        $cacheKey = "blog_post_{$slug}";

        return Cache::remember($cacheKey, 600, function () use ($slug) {
            try {
                $response = Http::timeout(10)->get("{$this->wordpressUrl}/wp-json/wp/v2/posts", [
                    'slug' => $slug,
                    '_embed' => true,
                    'status' => 'publish',
                ]);

                if ($response->successful()) {
                    $posts = $response->json();

                    if (!empty($posts)) {
                        return $this->formatPost($posts[0]);
                    }
                }

                return null;
            } catch (\Exception $e) {
                Log::error("Failed to fetch blog post with slug {$slug}: " . $e->getMessage());
                return null;
            }
        });
    }

    /**
     * Get featured posts
     */
    public function getFeaturedPosts(int $limit = 3): array
    {
        $cacheKey = "featured_blog_posts_{$limit}";

        return Cache::remember($cacheKey, 600, function () use ($limit) {
            try {
                $response = Http::timeout(10)->get("{$this->wordpressUrl}/wp-json/wp/v2/posts", [
                    'per_page' => $limit,
                    'sticky' => true,
                    '_embed' => true,
                    'status' => 'publish',
                ]);

                if ($response->successful()) {
                    $posts = $response->json();
                    return $this->formatPosts($posts);
                }

                return [];
            } catch (\Exception $e) {
                Log::error('Failed to fetch featured blog posts: ' . $e->getMessage());
                return [];
            }
        });
    }

    /**
     * Get related posts for a given post
     */
    public function getRelatedPosts(array $post, int $limit = 3): array
    {
        $categories = $post['categories'] ?? [];

        if (empty($categories)) {
            return $this->getPosts($limit)['posts'];
        }

        $cacheKey = "related_posts_" . $post['id'] . "_{$limit}";

        return Cache::remember($cacheKey, 600, function () use ($categories, $limit, $post) {
            try {
                // Get posts from same categories
                $response = Http::timeout(10)->get("{$this->wordpressUrl}/wp-json/wp/v2/posts", [
                    'per_page' => $limit + 5, // Get more to filter out current post
                    '_embed' => true,
                    'status' => 'publish',
                ]);

                if ($response->successful()) {
                    $posts = $response->json();
                    $relatedPosts = [];

                    foreach ($posts as $relatedPost) {
                        if ($relatedPost['id'] !== $post['id'] && count($relatedPosts) < $limit) {
                            $relatedPosts[] = $this->formatPost($relatedPost);
                        }
                    }

                    return $relatedPosts;
                }

                return [];
            } catch (\Exception $e) {
                Log::error("Failed to fetch related posts for post ID {$post['id']}: " . $e->getMessage());
                return [];
            }
        });
    }

    /**
     * Format multiple posts
     */
    private function formatPosts(array $posts): array
    {
        return array_map([$this, 'formatPost'], $posts);
    }

    /**
     * Format single post data
     */
    private function formatPost(array $post): array
    {
        $formatted = [
            'id' => $post['id'],
            'title' => $post['title']['rendered'] ?? '',
            'slug' => $post['slug'] ?? '',
            'excerpt' => strip_tags($post['excerpt']['rendered'] ?? ''),
            'content' => $post['content']['rendered'] ?? '',
            'published_at' => $post['date'] ?? '',
            'featured_image' => null,
            'author' => null,
            'categories' => [],
        ];

        // Extract featured image
        if (isset($post['_embedded']['wp:featuredmedia'][0]['source_url'])) {
            $formatted['featured_image'] = $post['_embedded']['wp:featuredmedia'][0]['source_url'];
        }

        // Extract author
        if (isset($post['_embedded']['author'][0]['name'])) {
            $formatted['author'] = $post['_embedded']['author'][0]['name'];
        }

        // Extract categories
        if (isset($post['_embedded']['wp:term'][0])) {
            $categories = $post['_embedded']['wp:term'][0];
            $formatted['categories'] = array_map(function ($category) {
                return $category['name'];
            }, $categories);
        }

        return $formatted;
    }
}
