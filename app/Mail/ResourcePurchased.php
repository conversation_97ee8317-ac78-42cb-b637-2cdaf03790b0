<?php

namespace App\Mail;

use App\Models\BoughtResource;
use App\Services\ResourcesService;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ResourcePurchased extends Mailable
{
    use Queueable, SerializesModels;

    public array $resource;
    public array $downloadFiles;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public BoughtResource $boughtResource,
        protected ResourcesService $resourcesService
    ) {
        // Get resource data
        $this->resource = $this->boughtResource->getResourceData() ?? $this->getMockResourceData();

        // Get download files for this resource
        $this->downloadFiles = $this->resourcesService->getFilesByProductId((int) $this->boughtResource->resource_id);

        // If no download files found, use mock data for preview
        if (empty($this->downloadFiles)) {
            $this->downloadFiles = $this->getMockDownloadFiles();
        }
    }

    /**
     * Get mock resource data for preview/testing
     */
    private function getMockResourceData(): array
    {
        return [
            'name' => 'Complete NAPLAN Practice Pack',
            'short_description' => 'Comprehensive practice materials for NAPLAN preparation including reading, writing, language conventions, and numeracy.'
        ];
    }

    /**
     * Get mock download files for preview/testing
     */
    private function getMockDownloadFiles(): array
    {
        return [
            [
                'name' => 'NAPLAN Reading Practice.pdf',
                'file' => 'https://example.com/downloads/naplan-reading.pdf'
            ],
            [
                'name' => 'NAPLAN Writing Guide.pdf',
                'file' => 'https://example.com/downloads/naplan-writing.pdf'
            ],
            [
                'name' => 'NAPLAN Numeracy Worksheets.pdf',
                'file' => 'https://example.com/downloads/naplan-numeracy.pdf'
            ]
        ];
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Your Resource Purchase - ' . ($this->resource['name'] ?? 'Digital Resource'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.resource-purchased',
            with: [
                'resource' => $this->resource,
                'downloadFiles' => $this->downloadFiles,
                'user' => $this->boughtResource->user,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
