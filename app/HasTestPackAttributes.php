<?php

namespace App;

use App\Naplan\TestPack;
use App\Naplan\TestPackRepository;
use Illuminate\Database\Eloquent\Casts\Attribute;

trait HasTestPackAttributes
{
    public function testPack(): TestPack
    {
        return app(TestPackRepository::class)->find($this->test_pack_id);
    }

    public function title(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->testPack()->Title,
        );
    }

    public function category(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->testPack()->Category,
        );
    }

    public function level(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->testPack()->Level,
        );
    }
}
