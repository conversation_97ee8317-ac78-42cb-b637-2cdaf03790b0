<?php

namespace App\Listeners;

use App\Actions\AssignResourceToUser;
use App\Actions\AssignTestPackToUser;
use Illuminate\Support\Facades\Log;
use LemonSqueezy\Laravel\Events\OrderCreated;

class OrderCreatedListener
{
    /**
     * Handle the event.
     */
    public function handle(OrderCreated $event): void
    {
        $customData = $event->payload['meta']['custom_data'] ?? [];
        $userId = $event->billable->id ?? null;

        Log::info('Processing LemonSqueezy order', [
            'order_id' => $event->payload['data']['id'] ?? null,
            'user_id' => $userId,
            'custom_data' => $customData,
        ]);

        // Handle test pack purchase
        if (isset($customData['pack_id'])) {
            Log::info('Processing test pack purchase', [
                'pack_id' => $customData['pack_id'],
                'user_id' => $userId,
            ]);

            AssignTestPackToUser::make()->handle(
                $customData['pack_id'],
                $event->billable,
            );
        }

        // Handle resource purchase
        if (isset($customData['resource_id'])) {
            Log::info('Processing resource purchase', [
                'resource_id' => $customData['resource_id'],
                'user_id' => $userId,
            ]);

            AssignResourceToUser::make()->handle(
                $customData['resource_id'],
                $event->billable,
            );
        }

        // Log if neither type was found
        if (!isset($customData['pack_id']) && !isset($customData['resource_id'])) {
            Log::warning('Order created but no pack_id or resource_id found in custom data', [
                'order_id' => $event->payload['data']['id'] ?? null,
                'user_id' => $userId,
                'custom_data' => $customData,
            ]);
        }
    }
}
