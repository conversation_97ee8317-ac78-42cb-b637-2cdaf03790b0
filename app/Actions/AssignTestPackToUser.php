<?php

namespace App\Actions;

use App\Models\BoughtTestPack;
use App\Models\User;
use App\Naplan\TestPackRepository;
use Lorisleiva\Actions\Concerns\AsAction;

class AssignTestPackToUser
{
    use AsAction;

    public function __construct(
        protected TestPackRepository $testPackRepository
    ) {}

    public function handle(int $testPackId, User $user): void
    {
        /** @var BoughtTestPack $boughtTestPack */
        $boughtTestPack = $user->boughtTestPacks()->updateOrCreate(
            ['test_pack_id' => $testPackId],
            ['expires_at' => now()->addYear()]
        );

        $this->testPackRepository->assignTestPack(
            $boughtTestPack->user->ulid,
            $boughtTestPack->test_pack_id,
            $boughtTestPack->created_at,
        );
    }
}
