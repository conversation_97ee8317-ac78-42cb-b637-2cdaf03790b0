<?php

namespace App\Actions;

use App\Models\AssignedTestPack;
use App\Models\BoughtTestPack;
use App\Models\Student;
use App\Models\User;
use App\Naplan\TestPackRepository;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class AssignTestPackToStudents
{
    use AsAction;

    public function __construct(
        protected TestPackRepository $testPackRepository
    ) {}

    public function handle(BoughtTestPack $boughtTestPack, array $studentIds): void
    {
        $testPackId = $boughtTestPack->test_pack_id;
        // reset keys to index
        $studentIds = array_values($studentIds);

        /** @var User $user */
        $user = user();

        $allStudentIds = $user->students()->select('id')->pluck('id')->toArray();

        // Ensure we only work with students that belong to the current user
        $validStudentIds = array_intersect($studentIds, $allStudentIds);

        // Get current assigned test packs for this test pack
        $currentIds = $boughtTestPack->students->pluck('id')->toArray();

        // Find students to remove from the test pack
        $removeIds = array_diff($currentIds, $validStudentIds);

        // Find students to add to the test pack
        $addIds = array_diff($validStudentIds, $currentIds);

        // Begin a database transaction for the modification operations
        DB::beginTransaction();

        try {
            // Remove students that are no longer in the list
            if (!empty($removeIds)) {
                $this->unassign($boughtTestPack, $removeIds);
            }

            // Create new assigned test packs in bulk for better performance
            if (!empty($addIds)) {
                $this->assign($boughtTestPack, $addIds);
            }

            // Commit the transaction if everything succeeded
            DB::commit();
        } catch (\Throwable $e) {
            // Roll back the transaction if something failed
            DB::rollBack();

            // Re-throw the exception for higher-level handling
            throw $e;
        }
    }

    public function assign(BoughtTestPack $boughtTestPack, array $studentIds): void
    {
        $records = [];

        foreach ($studentIds as $studentId) {
            $records[] = [
                'bought_test_pack_id' => $boughtTestPack->id,
                'test_pack_id' => $boughtTestPack->test_pack_id,
                'student_id'   => $studentId,
                'created_at'   => now(),
                'updated_at'   => now(),
            ];
        }

        AssignedTestPack::query()->insert($records);

        $students = Student::whereIn('id', $studentIds)->get();

        foreach ($students as $student) {
            $this->testPackRepository->assignTestPack(
                $student->ulid,
                $boughtTestPack->test_pack_id,
                $boughtTestPack->created_at
            );
        }
    }

    public function unassign(BoughtTestPack $boughtTestPack, array $removeIds): void
    {
        AssignedTestPack::query()
            ->where('bought_test_pack_id', $boughtTestPack->id)
            ->whereIn('student_id', $removeIds)
            ->delete();

        $students = Student::query()->whereIn('id', $removeIds)->get();

        foreach ($students as $student) {
            rescue(fn() => $this->testPackRepository->unassignTestPack($student->ulid, $boughtTestPack->test_pack_id));
        }
    }
}
