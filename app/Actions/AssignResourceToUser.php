<?php

namespace App\Actions;

use App\Mail\ResourcePurchased;
use App\Models\BoughtResource;
use App\Models\User;
use App\Services\ResourcesService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Lorisleiva\Actions\Concerns\AsAction;

class AssignResourceToUser
{
    use AsAction;

    public function __construct(
        protected ResourcesService $resourcesService
    ) {}

    public function handle(string $resourceId, User $user): BoughtResource
    {
        $boughtResource = $user->boughtResources()->updateOrCreate([
            'resource_id' => $resourceId
        ]);

        // Send email notification with download links
        try {
            Mail::to($user->email)->send(
                new ResourcePurchased($boughtResource, $this->resourcesService)
            );

            Log::info('Resource purchase email sent successfully', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'resource_id' => $resourceId
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send resource purchase email', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'resource_id' => $resourceId,
                'error' => $e->getMessage()
            ]);
        }

        return $boughtResource;
    }
}
