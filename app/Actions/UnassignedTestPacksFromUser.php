<?php

namespace App\Actions;

use App\Naplan\TestPackRepository;
use Lorisleiva\Actions\Concerns\AsAction;

class UnassignedTestPacksFromUser
{
    use AsAction;

    public function __construct(protected TestPackRepository $testPackRepository) {}

    public function handle(string $userULid, array $testPackIds): void
    {
        foreach ($testPackIds as $testPackId) {
            $this->testPackRepository->unassignTestPack(
                $userULid,
                $testPackId
            );
        }
    }
}
