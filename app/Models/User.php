<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Naplan\TestPack;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use LemonSqueezy\Laravel\Billable;
use LemonSqueezy\Laravel\Checkout;

class User extends Authenticatable implements FilamentUser, MustVerifyEmail
{
    use HasFactory, Notifiable, Billable, HasUlids;

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password'          => 'hashed',
        ];
    }

    public function uniqueIds(): array
    {
        return ['ulid'];
    }

    public function students(): HasMany
    {
        return $this->hasMany(Student::class);
    }

    public function boughtTestPacks(): HasMany
    {
        return $this->hasMany(BoughtTestPack::class);
    }

    public function boughtResources(): HasMany
    {
        return $this->hasMany(BoughtResource::class);
    }

    public function checkoutTestPackEmbedUrl(TestPack $testPack): string
    {
        return $this->checkoutTestPack($testPack)
            ->embed()
            ->url();
    }

    public function checkoutTestPackUrl(TestPack $testPack): string
    {
        $checkout =  $this->checkoutTestPack($testPack);

        return $checkout->url();
    }

    public function checkoutTestPack(TestPack $testPack): Checkout
    {
        return $this->charge(
            $testPack->getDiscountedPrice() * 100,
            config('aussie.lemon_squeeze_variant_id'),
        )
            ->withProductName($testPack->Title)
            ->withDescription($testPack->Description)
            ->withCustomData([
                'pack_id' => (string) $testPack->Id
            ])
            ->redirectTo(route('checkout.success') . '?type=test-pack&product_id=' . $testPack->Id . '&order_id=[order_id]&email=[email]&total=[total]');
    }

    public function checkoutResourceUrl(array $resource): string
    {
        return $this->checkoutResource($resource)->url();
    }

    public function checkoutResource(array $resource): Checkout
    {
        $price = $resource['sale_price'] ?: $resource['regular_price'];

        return $this->charge(
            $price * 100,
            config('aussie.lemon_squeeze_variant_id'),
        )
            ->withProductName($resource['name'])
            ->withDescription($resource['short_description'] ?? $resource['description'])
            ->withCustomData([
                'resource_id' => (string) $resource['id']
            ])
            ->redirectTo(route('checkout.success') . '?type=resource&product_id=' . $resource['id'] . '&order_id=[order_id]&email=[email]&total=[total]');
    }

    public function canAccessPanel(Panel $panel): bool
    {
        if ($panel->getId() === 'dashboard') {
            return true;
        }
    }
}
