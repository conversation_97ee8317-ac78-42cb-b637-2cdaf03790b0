<?php

namespace App\Models;

use App\HasTestPackAttributes;
use App\Naplan\TestPack;
use App\Naplan\TestPackRepository;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class BoughtTestPack extends Model
{
    use HasFactory;
    use HasTestPackAttributes;

    protected $casts = [
        'expires_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function testPack(): TestPack
    {
        return app(TestPackRepository::class)->find($this->test_pack_id);
    }

    public function students(): BelongsToMany
    {
        return $this->belongsToMany(
            Student::class,
            'assigned_test_packs',
            'bought_test_pack_id',
            'student_id'
        )
            ->using(AssignedTestPack::class)
            ->withPivot('test_pack_id')
            ->withTimestamps();
    }
}
