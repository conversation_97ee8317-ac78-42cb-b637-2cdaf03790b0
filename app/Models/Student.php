<?php

namespace App\Models;

use App\Actions\UnassignedTestPacksFromUser;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Student extends Authenticatable implements FilamentUser
{
    use HasFactory, Notifiable, HasUlids;

    protected $hidden = [
        'password',
    ];

    protected $casts = [
        'password' => 'hashed',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function assignedTestPacks(): HasMany
    {
        return $this->hasMany(AssignedTestPack::class);
    }

    public function uniqueIds(): array
    {
        return ['ulid'];
    }

    public function canAccessPanel(Panel $panel): bool
    {
        if ($panel->getId() === 'student') {
            return true;
        }

        return false;
    }

    /**
     * Get the avatar URL for the student.
     */
    protected function avatarUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => asset('images/avatars/' . ($this->avatar ?? 'avatar-1.png'))
        );
    }

    public function delete()
    {
        $testPackIds = $this->assignedTestPacks->pluck('test_pack_id')->toArray();
        UnassignedTestPacksFromUser::dispatch($this->ulid, $testPackIds);

        $this->assignedTestPacks()->delete();

        parent::delete();
    }
}
