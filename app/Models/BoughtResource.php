<?php

namespace App\Models;

use App\Services\ResourcesService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BoughtResource extends Model
{
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the resource data from WooCommerce
     */
    public function getResourceData(): ?array
    {
        $resourcesService = app(ResourcesService::class);
        $products = $resourcesService->getDigitalProducts(1, 100);

        foreach ($products as $product) {
            if ($product['id'] == $this->resource_id) {
                return $product;
            }
        }

        return null;
    }

    /**
     * Get the resource name
     */
    public function getResourceName(): string
    {
        $data = $this->getResourceData();
        return $data['name'] ?? 'Unknown Resource';
    }

    /**
     * Get the downloadable files for this resource
     */
    public function getDownloadableFiles(): array
    {
        $resourcesService = app(ResourcesService::class);
        return $resourcesService->getFilesByProductId((int) $this->resource_id);
    }
}
