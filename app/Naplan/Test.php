<?php

namespace App\Naplan;

class Test
{
    use FromArrayTrait;
    public function __construct(
        public int     $Id,
        public ?string $Title,
        public ?string $Description,
        public int     $TimeInMinutes,
        public int     $NumberOfQuestions,
        public string  $TestLevel,
        public int     $YearLevel,
        public string  $Category,
        public array   $QuestionIds,
        public array   $QuestionList,
        public string  $CreatedDate,
    )
    {
    }
}
