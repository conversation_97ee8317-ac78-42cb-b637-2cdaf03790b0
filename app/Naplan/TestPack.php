<?php

namespace App\Naplan;

use App\Services\DiscountService;

class TestPack
{
    use FromArrayTrait;

    protected ?DiscountService $discountService = null;

    public function __construct(
        public int     $Id,
        public ?string $Title,
        public float   $Price,
        public string  $Category,
        public int     $Level,
        public ?string $Description,
        public string  $CreatedDate,
        public int     $TotalTests,
        /** @var array<Test> $Tests */
        public array   $Tests,
    )
    {
        $this->Tests = array_map(fn($test) => Test::fromArray((array)$test), $Tests);
    }

    /**
     * Get the discount service instance with lazy initialization
     */
    private function getDiscountService(): DiscountService
    {
        if ($this->discountService === null) {
            $this->discountService = app(DiscountService::class);
        }

        return $this->discountService;
    }

    public function getBadgeAbbreviation(): string
    {
        if (!$this->Category) return 'ED';

        $category = strtolower($this->Category);

        return match ($category) {
            'naplanstyle' => 'N',
            'opportunity class', 'oc' => 'OC',
            'selective' => 'SH',
            default => strtoupper(substr($this->Category, 0, 2))
        };
    }

    public function getBackgroundColor(): string
    {
        $colors = [
            3 => '#4CAF50', // Updated color for level 3 to a modern green shade
            4 => '#5CBA4A',
            5 => '#0FBACF',
            6 => '#08C',
            7 => '#FE5559',
            8 => '#1ABC9C',
            9 => '#9B59B6',
        ];

        return $colors[$this->Level] ?? '#4A90E2'; // Default color
    }

    public function getTotalQuestions(): int
    {
        $totalQuestions = 0;

        foreach ($this->Tests as $test) {
            $totalQuestions += $test->NumberOfQuestions;
        }

        return $totalQuestions;
    }

    public function getCategoryLabel(): string
    {
        return match ($this->Category) {
            'naplanStyle' => 'NAPLAN',
            'opportunityClass' => 'Opportunity Class',
            'selective' => 'Selective',
            default => $this->Category,
        };
    }

    public function hasTests(): bool
    {
        return $this->TotalTests > 0 && !empty($this->Tests);
    }

    /**
     * Check if there's an active discount for test packs
     */
    public function hasActiveDiscount(): bool
    {
        return $this->getDiscountService()->hasActiveDiscount();
    }

    /**
     * Get the discounted price if discount is active, otherwise return original price
     */
    public function getDiscountedPrice(): float
    {
        return $this->getDiscountService()->calculateDiscountedPrice($this->Price);
    }

    /**
     * Get the discount percentage if active
     */
    public function getDiscountPercent(): int
    {
        return $this->getDiscountService()->getDiscountPercent();
    }

    /**
     * Get the amount saved if discount is active
     */
    public function getSavingsAmount(): float
    {
        return $this->getDiscountService()->calculateSavingsAmount($this->Price);
    }
}
