<?php

namespace App\Naplan;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Http;

class Backend
{
    public function __construct(
        public string    $url,
        protected string $token
    ) {}

    /**
     * @throws NaplanException
     */
    public function request(string $method, string $endpoint, array $data = []): array
    {
        $url = rtrim($this->url, '/') . '/' . ltrim($endpoint, '/');

        $request = Http::withToken($this->token);

        if (!App::isProduction()) {
            $request = $request->withHeaders([
                'X-API-KEY' => 'XCrossAPIkeyLocalhost',
            ]);
        }
        $response = $request->$method($url, $data);

        if ($response->failed()) {
            throw new NaplanException('Request failed: ' . $response->body());
        }

        return $response->json();
    }
}
