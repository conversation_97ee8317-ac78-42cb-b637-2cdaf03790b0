<?php

namespace App\Naplan;

use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class TestPackRepository
{
    public array $flexibleCacheTtl = [60, 300000];

    public function __construct(protected Backend $backend)
    {
    }

    public function find(string $packId): TestPack
    {
        return Cache::flexible("naplan.testpack.$packId", $this->flexibleCacheTtl, function () use ($packId) {
            $result = $this->backend->request('get', "/testpacks/$packId");
            return TestPack::fromArray($result);
        });
    }

    public function allTestPacks(): array
    {
        return Cache::flexible('naplan.testpacks', $this->flexibleCacheTtl, function () {
            $items = [];
            $page = 1;

            do {
                $result = $this->backend->request('get', '/testpacks', ['page' => $page]);
                $items =[...$items, ...$result['TestPacks']];

                $page++;
            } while ($result['TotalPages'] > $result['CurrentPage']);

            $testpacks = [];

            foreach ($items as $item) {
                $testpacks[] = TestPack::fromArray($item);
            }

            // Sort test packs: those with tests first, those without tests at the end
            usort($testpacks, function ($a, $b) {
                // If both have tests or both don't have tests, maintain original order
                if (($a->TotalTests > 0) === ($b->TotalTests > 0)) {
                    return 0;
                }
                // Put test packs with tests first (return -1 if $a has tests and $b doesn't)
                return ($a->TotalTests > 0) ? -1 : 1;
            });

            return $testpacks;
        });
    }

    public function assignTestPack(string $uid, string $testPackId, string|Carbon $assignedDate): void
    {
        $this->backend->request(
            'post',
            '/testpacks/assign',
            [
                'Uid'          => $uid,
                'TestPackId'   => $testPackId,
                'AssignedDate' => $assignedDate instanceof Carbon ? $assignedDate->toIso8601String() : $assignedDate,
            ]
        );
    }

    public function unassignTestPack(string $uid, string $testPackId): void
    {
        $this->backend->request(
            'delete',
            "/testpacks/unassign/$uid/$testPackId",
        );
    }
}
