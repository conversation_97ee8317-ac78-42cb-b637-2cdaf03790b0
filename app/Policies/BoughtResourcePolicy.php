<?php

namespace App\Policies;

use App\Models\BoughtResource;
use App\Models\User;

class BoughtResourcePolicy
{
    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, BoughtResource $boughtResource): bool
    {
        return $user->id === $boughtResource->user_id;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, BoughtResource $boughtResource): bool
    {
        return $user->id === $boughtResource->user_id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, BoughtResource $boughtResource): bool
    {
        return $user->id === $boughtResource->user_id;
    }
}
