@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        scroll-behavior: smooth;
        scrollbar-gutter: stable;
    }

    body {
        @apply bg-background text-foreground;
    }

    /* Apply the font to all heading elements */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Onest', sans-serif;
    }

    /* Optional: Add specific styling for different heading levels */
    h1 {
        font-weight: 800;
        letter-spacing: -0.025em;
    }

    h2 {
        font-weight: 700;
        letter-spacing: -0.025em;
        font-size: 1.75rem; /* 28px */
        line-height: 1.2;
    }

    /* Make h2 bigger on large screens */
    @media (min-width: 1024px) {
        h2 {
            font-size: 2.25rem; /* 36px */
            line-height: 1.1;
        }
    }

    h3 {
        font-weight: 600;
    }

    h4, h5, h6 {
        font-weight: 500;
    }
}

@layer utilities {
    .wp-video {
        max-width: 100%;
    }
}
