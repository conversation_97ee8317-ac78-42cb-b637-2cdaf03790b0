<x-layouts.app
    title="{{ $post['title'] }} - Blog - {{ config('app.name') }}"
    :description="$post['excerpt'] ?? Str::limit(strip_tags($post['content']), 160)"
    ogType="article"
    :ogTitle="$post['title']"
    :ogDescription="$post['excerpt'] ?? Str::limit(strip_tags($post['content']), 160)"
    :ogImage="$post['featured_image'] ?? null"
>
    <main class="container mx-auto max-w-4xl py-8 px-6">
        <!-- Breadcrumb -->
        <nav class="mb-8">
            <ol class="flex items-center space-x-2 text-sm text-gray-500">
                <li>
                    <a href="{{ route('home') }}" class="hover:text-primary transition-colors">
                        Home
                    </a>
                </li>
                <li>
                    <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </li>
                <li>
                    <a href="{{ route('blog.index') }}" class="hover:text-primary transition-colors">
                        Blog
                    </a>
                </li>
                <li>
                    <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </li>
                <li class="text-gray-900 font-medium">
                    {{ Str::limit($post['title'], 40) }}
                </li>
            </ol>
        </nav>

        <article class="bg-white rounded-lg overflow-hidden">
            <!-- Featured Image -->
            @if($post['featured_image'])
                <div class="aspect-video overflow-hidden">
                    <img
                        src="{{ $post['featured_image'] }}"
                        alt="{{ $post['title'] }}"
                        class="w-full h-full object-cover"
                        loading="lazy"
                    >
                </div>
            @endif

            <div class="p-8">
                <!-- Categories -->
                @if(!empty($post['categories']))
                    <div class="flex flex-wrap gap-2 mb-4">
                        @foreach($post['categories'] as $category)
                            <span class="inline-block px-3 py-1 text-sm font-medium bg-primary/10 text-primary rounded-full">
                                {{ $category }}
                            </span>
                        @endforeach
                    </div>
                @endif

                <!-- Title -->
                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    {{ $post['title'] }}
                </h1>

                <!-- Meta Information -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
                    <div class="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6 text-sm text-gray-600">
                        @if($post['author'])
                            <div class="flex items-center gap-2">
                                <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span class="font-medium">{{ $post['author'] }}</span>
                            </div>
                        @endif

                        <div class="flex items-center gap-2">
                            <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span>{{ \Carbon\Carbon::parse($post['published_at'])->format('M j, Y') }}</span>
                        </div>
                    </div>

                    <!-- Share Buttons -->
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-gray-600 mr-2 hidden sm:inline">Share:</span>
                        <span class="text-sm text-gray-600 mr-2 sm:hidden">Share</span>
                        <a
                            href="https://twitter.com/intent/tweet?text={{ urlencode($post['title']) }}&url={{ urlencode(request()->url()) }}"
                            target="_blank"
                            rel="noopener"
                            class="p-1.5 sm:p-2 text-gray-600 hover:text-blue-500 hover:bg-blue-50 rounded-full transition-colors"
                            title="Share on Twitter"
                        >
                            <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                        <a
                            href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}"
                            target="_blank"
                            rel="noopener"
                            class="p-1.5 sm:p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                            title="Share on Facebook"
                        >
                            <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                        <a
                            href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->url()) }}"
                            target="_blank"
                            rel="noopener"
                            class="p-1.5 sm:p-2 text-gray-600 hover:text-blue-700 hover:bg-blue-50 rounded-full transition-colors"
                            title="Share on LinkedIn"
                        >
                            <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Post Content -->
                <div class="prose max-w-none">
                    {!! $post['content'] !!}
                </div>
            </div>
        </article>

        <!-- Related Posts -->
        @if(!empty($relatedPosts))
            <section class="mt-16">
                <h2 class="text-2xl font-bold text-gray-900 mb-8">Related Articles</h2>
                <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    @foreach($relatedPosts as $relatedPost)
                        <article class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                            @if($relatedPost['featured_image'])
                                <div class="aspect-video overflow-hidden">
                                    <img
                                        src="{{ $relatedPost['featured_image'] }}"
                                        alt="{{ $relatedPost['title'] }}"
                                        class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                                    >
                                </div>
                            @endif

                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                    <a href="{{ route('blog.show', $relatedPost['slug']) }}" class="hover:text-primary transition-colors">
                                        {{ $relatedPost['title'] }}
                                    </a>
                                </h3>

                                @if($relatedPost['excerpt'])
                                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                                        {{ $relatedPost['excerpt'] }}
                                    </p>
                                @endif

                                <div class="flex items-center justify-between text-xs text-gray-500">
                                    <span>{{ \Carbon\Carbon::parse($relatedPost['published_at'])->format('M j, Y') }}</span>
                                    <a
                                        href="{{ route('blog.show', $relatedPost['slug']) }}"
                                        class="text-primary hover:text-primary/80 font-medium transition-colors"
                                    >
                                        Read more →
                                    </a>
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>
            </section>
        @endif
    </main>
</x-layouts.app>
