<x-layouts.app title="Blog - {{ config('app.name') }}">
    <main class="container mx-auto max-w-7xl py-8 px-6">
        <!-- Page Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Education Blog</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Stay updated with the latest insights, tips, and resources for your child's educational journey
            </p>
        </div>

        @if(!empty($posts['posts']))
            <!-- Blog Posts Grid -->
            <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                @foreach($posts['posts'] as $post)
                    <x-blog-post-card :post="$post" />
                @endforeach
            </div>

            <!-- Pagination -->
            @if($posts['total_pages'] > 1)
                <div class="mt-12 flex justify-center">
                    <nav class="flex items-center gap-2">
                        @php
                            $currentPage = request('page', 1);
                            $totalPages = $posts['total_pages'];
                        @endphp

                        @if($currentPage > 1)
                            <a
                                href="{{ route('blog.index') }}?page={{ $currentPage - 1 }}"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                            >
                                Previous
                            </a>
                        @endif

                        @for($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++)
                            @if($i == $currentPage)
                                <span class="px-4 py-2 text-sm font-medium text-white bg-primary border border-primary rounded-md">
                                    {{ $i }}
                                </span>
                            @else
                                <a
                                    href="{{ route('blog.index') }}?page={{ $i }}"
                                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                                >
                                    {{ $i }}
                                </a>
                            @endif
                        @endfor

                        @if($currentPage < $totalPages)
                            <a
                                href="{{ route('blog.index') }}?page={{ $currentPage + 1 }}"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                            >
                                Next
                            </a>
                        @endif
                    </nav>
                </div>
            @endif
        @else
            <!-- No Posts Found -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No blog posts found</h3>
                <p class="text-gray-600">
                    We're working on creating great content for you. Check back soon!
                </p>
            </div>
        @endif
    </main>
</x-layouts.app>
