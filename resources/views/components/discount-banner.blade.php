@php
    $discountService = app(\App\Services\DiscountService::class);
    $discountInfo = $discountService->getDiscountInfo();
@endphp

@if($discountInfo['is_active'] || true)
    <div id="discount-banner" class="z-50 bg-gradient-to-r from-rose-500 to-pink-600 text-white py-2 px-4 overflow-hidden">
        <div class="container mx-auto max-w-7xl relative z-10">
            <div class="flex items-center justify-center text-center px-2 sm:px-0">
                <div class="flex items-center gap-2 sm:gap-3 flex-wrap justify-center">
                    <!-- Discount text -->
                    <div class="font-semibold text-sm sm:text-base">
                        <span class="text-yellow-200">🚀</span>
                        <span class="ml-1">Free Access to All Test Packs - Limited Time Offer!</span>
                        <span class="text-yellow-200 ml-1">📚</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    </script>
@else
    <script>
        // Remove CSS variable when banner is not active
    </script>
@endif