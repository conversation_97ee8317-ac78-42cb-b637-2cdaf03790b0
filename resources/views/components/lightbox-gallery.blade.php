@props([
    'images' => [],
    'title' => '',
    'mainImageClass' => 'w-full h-full object-cover cursor-pointer',
    'thumbnailClass' => 'w-full h-full object-cover',
    'showThumbnails' => true
])

@php
    $galleryId = 'gallery-' . uniqid();
    $modalId = 'modal-' . uniqid();
    $hasMultipleImages = count($images) > 1;
@endphp

<div class="space-y-4 p-4" x-data="lightboxGallery('{{ $galleryId }}', {{ json_encode($images) }})">
    <!-- Main Image -->
    <div class="aspect-w-16 aspect-h-12 bg-gray-200 rounded-lg overflow-hidden relative group">
        @if(count($images) > 0)
            <img
                x-ref="mainImage"
                :src="currentImage.src"
                :alt="currentImage.alt || '{{ $title }}'"
                class="{{ $mainImageClass }}"
                loading="lazy"
                @click="openLightbox()"
            >

            <!-- Navigation Arrows for Main Image -->
            <template x-if="images.length > 1">
                <div>
                    <!-- Previous Button -->
                    <button
                        @click.stop="previousImage()"
                        class="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-2 rounded-full transition-all duration-200"
                        :class="{ 'opacity-50 cursor-not-allowed': currentIndex === 0 }"
                        :disabled="currentIndex === 0"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>

                    <!-- Next Button -->
                    <button
                        @click.stop="nextImage()"
                        class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-2 rounded-full transition-all duration-200"
                        :class="{ 'opacity-50 cursor-not-allowed': currentIndex === images.length - 1 }"
                        :disabled="currentIndex === images.length - 1"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>

                    <!-- Image Counter for Main Image -->
                    <div class="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                        <span x-text="currentIndex + 1"></span> / <span x-text="images.length"></span>
                    </div>
                </div>
            </template>
        @else
            <div class="w-full h-full bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center">
                <svg class="w-24 h-24 text-primary/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
        @endif
    </div>

    <!-- Gallery Thumbnails -->
    @if($showThumbnails && $hasMultipleImages)
        <div class="grid grid-cols-4 gap-2">
            <template x-for="(image, index) in images" :key="index">
                <div
                    class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-md overflow-hidden cursor-pointer hover:opacity-75 transition-opacity"
                    @click="setCurrentImage(index)"
                    :class="{ 'ring-2 ring-primary': currentIndex === index }"
                >
                    <img
                        :src="image.src"
                        :alt="image.alt || '{{ $title }} - Image ' + (index + 1)"
                        class="{{ $thumbnailClass }}"
                        loading="lazy"
                    >
                </div>
            </template>
        </div>
    @endif

    <!-- Lightbox Modal -->
    <div
        x-show="isOpen"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
        @click="closeLightbox()"
        @keydown.escape.window="closeLightbox()"
        @keydown.arrow-left.window="previousImage()"
        @keydown.arrow-right.window="nextImage()"
        @keydown.h.window="previousImage()"
        @keydown.l.window="nextImage()"
        style="display: none;"
    >
        <div class="relative max-w-7xl max-h-full w-full h-full flex items-center justify-center" @click.stop>
            <!-- Main Lightbox Image -->
            <img
                :src="currentImage.src"
                :alt="currentImage.alt || '{{ $title }}'"
                class="max-w-full max-h-full object-contain"
                loading="lazy"
            >

            <!-- Navigation Arrows -->
            <template x-if="images.length > 1">
                <div>
                    <!-- Previous Button -->
                    <button
                        @click="previousImage()"
                        class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-3 rounded-full transition-all duration-200"
                        :class="{ 'opacity-50 cursor-not-allowed': currentIndex === 0 }"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>

                    <!-- Next Button -->
                    <button
                        @click="nextImage()"
                        class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-3 rounded-full transition-all duration-200"
                        :class="{ 'opacity-50 cursor-not-allowed': currentIndex === images.length - 1 }"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>
            </template>

            <!-- Close Button -->
            <button
                @click="closeLightbox()"
                class="absolute top-4 right-4 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-2 rounded-full transition-all duration-200"
            >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <!-- Image Counter -->
            <template x-if="images.length > 1">
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-4 py-2 rounded-full text-sm">
                    <span x-text="currentIndex + 1"></span> / <span x-text="images.length"></span>
                </div>
            </template>
        </div>
    </div>
</div>

<script>
function lightboxGallery(galleryId, images) {
    return {
        images: images || [],
        currentIndex: 0,
        isOpen: false,

        get currentImage() {
            return this.images[this.currentIndex] || { src: '', alt: '' };
        },

        setCurrentImage(index) {
            if (index >= 0 && index < this.images.length) {
                this.currentIndex = index;
            }
        },

        openLightbox() {
            if (this.images.length > 0) {
                this.isOpen = true;
                document.body.style.overflow = 'hidden';
            }
        },

        closeLightbox() {
            this.isOpen = false;
            document.body.style.overflow = 'auto';
        },

        nextImage() {
            if (this.currentIndex < this.images.length - 1) {
                this.currentIndex++;
            }
        },

        previousImage() {
            if (this.currentIndex > 0) {
                this.currentIndex--;
            }
        },

        init() {
            // Bind the handleKeydown method to this context
            this.boundHandleKeydown = this.handleKeydown.bind(this);

            // Keyboard navigation
            this.$watch('isOpen', (isOpen) => {
                if (isOpen) {
                    document.addEventListener('keydown', this.boundHandleKeydown);
                } else {
                    document.removeEventListener('keydown', this.boundHandleKeydown);
                }
            });
        },

        handleKeydown(e) {
            console.log(e.key);
            if (!this.isOpen) return;

            switch(e.key) {
                case 'ArrowLeft':
                case 'h':
                case 'H':
                    e.preventDefault();
                    this.previousImage();
                    break;
                case 'ArrowRight':
                case 'l':
                case 'L':
                    e.preventDefault();
                    this.nextImage();
                    break;
                case 'Escape':
                    e.preventDefault();
                    this.closeLightbox();
                    break;
            }
        }
    }
}
</script>
