@props(['pack'])

<div class="rounded-md overflow-hidden shadow-sm bg-gray-100 flex flex-col transition-all duration-300 h-full border border-gray-50 relative @if(!$pack->hasTests()) opacity-75 grayscale-[25%] @else hover:shadow-md hover:-translate-y-1 @endif">
    @if(!$pack->hasTests())
        <div class="absolute inset-0 bg-gray-200/15 pointer-events-none z-10 rounded-md"></div>
        <div class="absolute top-3 right-3 z-20">
            <span class="bg-amber-100 text-amber-800 text-xs font-medium px-2 py-1 rounded-full border border-amber-200">
                Coming Soon
            </span>
        </div>
    @elseif($pack->getDiscountedPrice() == 0)
        <!-- Large diagonal FREE banner -->
        <div class="absolute inset-0 z-20 pointer-events-none overflow-hidden rounded-md">
            <div class="absolute top-0 right-0 bg-rose-500 text-white transform rotate-45 origin-top-right shadow-lg"
                style="width: 300px; height: 50px; transform: rotate(45deg) translate(150px, 50px);">
                <div class="flex items-center justify-center h-full font-bold text-xl tracking-wider">
                    FREE
                </div>
            </div>
        </div>
    @elseif($pack->hasActiveDiscount())
        <div class="absolute top-3 right-3 z-20">
            <span class="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                {{ $pack->getDiscountPercent() }}% OFF
            </span>
        </div>
    @endif
    <!-- Colored header section -->
    <div class="p-6 text-white flex items-center justify-center @if(!$pack->hasTests()) opacity-90 @endif" style="background-color: {{ $pack->getBackgroundColor() }}">
        <div class="mr-4 w-20 h-20 bg-white rounded-lg flex items-center justify-center text-center">
            <div class="flex flex-col">
                <span class="text-3xl font-bold" style="color: {{ $pack->getBackgroundColor() }}">
                    {{ $pack->getBadgeAbbreviation() }}
                </span>
            </div>
        </div>
        <div>
            <div class="text-base">Year {{ $pack->Level }}</div>
            <h3 class="text-xl font-bold mb-2">
                {{ $pack->getCategoryLabel() }}
            </h3>
            <div class="inline-block bg-white/20 text-white text-sm rounded-full px-3 py-1">
                @if($pack->hasTests())
                    @if($pack->getDiscountedPrice() == 0)
                        <span class="font-bold text-green-200">FREE</span>
                    @elseif($pack->hasActiveDiscount())
                        <div class="flex items-center gap-1">
                            <span class="line-through opacity-75">${{ number_format($pack->Price, 2) }}</span>
                            <span class="font-bold">${{ number_format($pack->getDiscountedPrice(), 2) }}</span>
                        </div>
                    @else
                        ${{ number_format($pack->Price, 2) }}
                    @endif
                @else
                    Coming Soon
                @endif
            </div>
        </div>
    </div>

    <!-- Main content section -->
    <div class="p-6 flex-grow flex flex-col @if(!$pack->hasTests()) text-gray-500 @endif">
        <div class="text-center mb-6">
            <h2 class="text-3xl font-bold mb-3">Year {{ $pack->Level }}</h2>
            <h3 class="text-xl font-bold mb-4 flex items-center justify-center line-clamp-3">{{ $pack->Title }}</h3>
            <p class="text-gray-600 text-sm h-12 overflow-hidden line-clamp-2">{{ $pack->Description }}</p>
        </div>

        <div class="mt-auto flex gap-4 justify-center">
            @if($pack->hasTests())
                @if($pack->getDiscountedPrice() == 0)
                    <a href="{{ route('testPacks.show', $pack->Id) }}"
                       class="px-4 py-2 rounded-full text-sm bg-green-600 text-white font-medium text-center transition-colors hover:bg-green-700">
                        Take It Now - FREE
                    </a>
                @else
                    <a href="{{ route('testPacks.show', $pack->Id) }}"
                       class="px-4 py-2 rounded-full text-sm bg-primary text-white font-medium text-center transition-colors hover:bg-primary/90">
                        Buy ${{ number_format($pack->getDiscountedPrice(), 2) }}
                    </a>
                @endif
            @else
                <span class="px-4 py-2 rounded-full text-sm bg-amber-100 text-amber-800 font-medium text-center cursor-default">
                    Coming Soon
                </span>
            @endif
        </div>
    </div>
</div>
