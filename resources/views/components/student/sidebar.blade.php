<x-dashboard.base-sidebar>
            <!-- Student Profile -->
            <div class="px-4 py-4 mb-4">
                <div class="flex items-center" :class="{'justify-center': !sidebarOpen, 'space-x-3': sidebarOpen}">
                    <div class="flex-shrink-0">
                        <img
                            src="{{ student()->avatar_url }}"
                            alt="{{ student()->name }}"
                            :class="{'h-8 w-8': !sidebarOpen, 'h-10 w-10': sidebarOpen}"
                            class="rounded-full object-cover border border-primary"
                        >
                    </div>
                    <div class="flex-1 min-w-0" x-show="sidebarOpen">
                        <p class="text-sm font-medium text-gray-900 truncate">
                            {{ student()->name }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Dashboard -->
            <x-student.sidebar-link
                href="{{ route('student.dashboard') }}"
                :active="request()->routeIs('student.dashboard')"
            >
                <x-slot name="icon">
                    <x-heroicon-o-home class="h-5 w-5" />
                </x-slot>
                Dashboard
            </x-student.sidebar-link>

            <!-- Settings -->
            <x-student.sidebar-link
                href="{{ route('student.settings') }}"
                :active="request()->routeIs('student.settings')"
            >
                <x-slot name="icon">
                    <x-heroicon-o-cog class="h-5 w-5" />
                </x-slot>
                Settings
            </x-student.sidebar-link>
        </nav>

    <x-slot name="footer">
        <form method="POST" action="{{ route('student.logout') }}">
            @csrf
            <x-student.sidebar-button type="submit">
                <x-slot name="icon">
                    <x-heroicon-o-arrow-left-start-on-rectangle class="h-5 w-5" />
                </x-slot>
                Logout
            </x-student.sidebar-button>
        </form>
    </x-slot>
</x-dashboard.base-sidebar>
