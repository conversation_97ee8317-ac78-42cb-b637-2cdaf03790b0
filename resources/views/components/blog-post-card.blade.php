@props(['post'])

<article class="bg-white rounded-xl border border-gray-200 hover:border-gray-300 transition-colors duration-300 overflow-hidden group h-full flex flex-col">
    @if($post['featured_image'])
        <div class="aspect-video overflow-hidden">
            <img
                src="{{ $post['featured_image'] }}"
                alt="{{ $post['title'] }}"
                loading="lazy"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            >
        </div>
    @endif

    <div class="p-6 flex flex-col flex-grow">
        <!-- Categories -->
        @if(!empty($post['categories']))
            <div class="flex flex-wrap gap-2 mb-3">
                @foreach(array_slice($post['categories'], 0, 2) as $category)
                    <span class="inline-block px-3 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full">
                        {{ $category }}
                    </span>
                @endforeach
            </div>
        @endif

        <!-- Title -->
        <h3 class="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
            <a href="{{ route('blog.show', $post['slug']) }}" class="hover:text-primary transition-colors">
                {{ $post['title'] }}
            </a>
        </h3>

        <!-- Excerpt -->
        @if($post['excerpt'])
            <p class="text-gray-600 mb-4 line-clamp-3 flex-grow">
                {{ strip_tags($post['excerpt']) }}
            </p>
        @endif

        <!-- Meta & Read More - Always at bottom -->
        <div class="flex items-center justify-between mt-auto pt-4">
            <div class="flex items-center gap-4 text-sm text-gray-500">
                @if($post['author'])
                    <span class="flex items-center gap-1">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        {{ $post['author'] }}
                    </span>
                @endif

                <span class="flex items-center gap-1">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    {{ \Carbon\Carbon::parse($post['published_at'])->format('M j, Y') }}
                </span>
            </div>

            <a
                href="{{ route('blog.show', $post['slug']) }}"
                class="text-gray-600 hover:text-gray-800 font-medium text-sm transition-colors group"
            >
                Read more
                <svg class="inline h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>
    </div>
</article>
