<!-- Modern Header Component -->
<header class="sticky z-40 w-full backdrop-blur-md bg-white/90 shadow-sm border-b border-gray-100" style="top: var(--discount-banner-height, 0px);">
    <!-- Discount Banner - appears at the very top -->
    <x-discount-banner />

    <div class="container mx-auto max-w-7xl py-3 px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
            <!-- Logo and Navigation -->
            <div class="flex items-center gap-8 lg:gap-12">
                <a href="{{ route('home') }}" class="flex items-center">
                    <x-app-logo class="h-10 w-auto" />
                </a>

                <nav class="hidden md:flex items-center gap-1">
                    <a href="{{ route('test-packs.index') }}" class="px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-primary transition-colors">
                        Shop Test Packs
                    </a>
                    <a href="{{ route('resources.index') }}" class="px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-primary transition-colors">
                        Resources
                    </a>
                    <a href="{{ route('blog.index') }}" class="px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-primary transition-colors">
                        Blog
                    </a>
                    <a href="{{ route('pages.contact') }}" class="px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-primary transition-colors">
                        Contact
                    </a>
                </nav>
            </div>

            <!-- Auth Buttons -->
            <div class="hidden md:flex items-center gap-3">
                @auth('web')
                    <a href="{{ route('dashboard') }}" class="inline-flex items-center justify-center rounded-md bg-primary/10 text-primary hover:bg-primary/20 transition-colors px-5 py-2 text-sm font-medium">
                        <x-heroicon-o-home class="h-4 w-4 mr-2" />
                        Parent Dashboard
                    </a>
                @elseauth('student')
                    <a href="{{ route('student.dashboard') }}" class="inline-flex items-center justify-center rounded-md bg-primary/10 text-primary hover:bg-primary/20 transition-colors px-5 py-2 text-sm font-medium">
                        <x-heroicon-o-academic-cap class="h-4 w-4 mr-2" />
                        Student Dashboard
                    </a>
                @else
                    <div class="flex items-center divide-x divide-gray-200">
                        <div class="pr-3 flex items-center gap-3">
                            <a href="{{ route('login') }}" class="inline-flex items-center gap-1.5 text-gray-600 text-sm font-medium hover:text-primary transition-colors">
                                <x-heroicon-o-user class="h-4 w-4" />
                                <span>Parent Login</span>
                            </a>
                            <a href="{{ route('student.login') }}" class="inline-flex items-center gap-1.5 text-gray-600 text-sm font-medium hover:text-primary transition-colors">
                                <x-heroicon-o-academic-cap class="h-4 w-4" />
                                <span>Student Login</span>
                            </a>
                        </div>
                        <div class="pl-3">
                            <a href="{{ route('register') }}" class="inline-flex items-center justify-center rounded-md bg-primary text-white hover:bg-primary/90 transition-colors px-5 py-2 text-sm font-medium">
                                <x-heroicon-o-user-plus class="h-4 w-4 mr-1.5" />
                                Register
                            </a>
                        </div>
                    </div>
                @endauth
            </div>

            <!-- Mobile Menu Button -->
            <div class="md:hidden">
                <button
                    type="button"
                    class="p-2 rounded-md text-gray-600 hover:text-primary hover:bg-gray-100 focus:outline-none transition-colors"
                    onclick="document.getElementById('mobile-menu').classList.toggle('hidden')"
                    aria-label="Toggle menu"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Menu (hidden by default) -->
    <div id="mobile-menu" class="hidden md:hidden border-t border-gray-200 bg-white">
        <div class="container mx-auto max-w-7xl px-4 py-3 space-y-3">
            <nav class="grid gap-y-2">
                <a href="{{ route('test-packs.index') }}" class="px-4 py-2.5 text-base font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-primary transition-colors flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    Shop Test Packs
                </a>
                <a href="{{ route('resources.index') }}" class="px-4 py-2.5 text-base font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-primary transition-colors flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Resources
                </a>
                <a href="{{ route('blog.index') }}" class="px-4 py-2.5 text-base font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-primary transition-colors flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    Blog
                </a>
                <a href="{{ route('pages.about') }}" class="px-4 py-2.5 text-base font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-primary transition-colors flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    About Us
                </a>
                <a href="{{ route('pages.contact') }}" class="px-4 py-2.5 text-base font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-primary transition-colors flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Contact
                </a>
            </nav>

            @auth('web')
                <div class="pt-4 border-t border-gray-200">
                    <a href="{{ route('dashboard') }}" class="block w-full text-center rounded-md bg-primary text-white hover:bg-primary/90 transition-colors px-5 py-3 font-medium">
                        Parent Dashboard
                    </a>
                </div>
            @elseauth('student')
                <div class="pt-4 border-t border-gray-200">
                    <a href="{{ route('student.dashboard') }}" class="block w-full text-center rounded-md bg-primary text-white hover:bg-primary/90 transition-colors px-5 py-3 font-medium">
                        Student Dashboard
                    </a>
                </div>
            @else
                <div class="pt-4 border-t border-gray-200 space-y-3">
                    <div class="grid grid-cols-2 gap-3">
                        <a href="{{ route('login') }}" class="flex items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                            <x-heroicon-o-user class="h-5 w-5 text-gray-500" />
                            <span>Parent Login</span>
                        </a>
                        <a href="{{ route('student.login') }}" class="flex items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                            <x-heroicon-o-academic-cap class="h-5 w-5 text-gray-500" />
                            <span>Student Login</span>
                        </a>
                    </div>
                    <a href="{{ route('register') }}" class="block w-full text-center rounded-md bg-primary text-white hover:bg-primary/90 transition-colors px-5 py-3 font-medium">
                        Register
                    </a>
                </div>
            @endauth
        </div>
    </div>
</header>
