<section {{ $attributes->class("py-20 px-6") }}>
    <div class="container mx-auto max-w-7xl">
        <h2 class="text-3xl font-bold mb-8 text-center text-primary">Interactive Question Types</h2>
        <p class="text-center mb-12 max-w-3xl mx-auto text-gray-600">{{ config('app.name') }} supports a wide range of interactive question formats designed
            specifically for Australian students:</p>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Row 1 -->
            <x-question-type-card
                title="Multiple Choice"
                description="Traditional format where students select one correct answer from several options."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <path d="M8 6h13"></path>
                    <path d="M8 12h13"></path>
                    <path d="M8 18h13"></path>
                    <circle cx="3" cy="6" r="1"></circle>
                    <circle cx="3" cy="12" r="1"></circle>
                    <circle cx="3" cy="18" r="1"></circle>
                </svg>'
            />

            <x-question-type-card
                title="Sentence Selection"
                description="Students click on specific words within a sentence to select their answers. The sentence contains multiple highlighted words that can be selected."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                    <path d="M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v4"></path>
                    <path d="M10 12h8"></path>
                    <path d="M10 16h8"></path>
                    <path d="M10 20h4"></path>
                </svg>'
            />

            <x-question-type-card
                title="Selection Multiple"
                description="Students select multiple correct answers from a set of options."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <rect x="3" y="5" width="6" height="6" rx="1"></rect>
                    <rect x="15" y="5" width="6" height="6" rx="1"></rect>
                    <rect x="3" y="13" width="6" height="6" rx="1"></rect>
                    <rect x="15" y="13" width="6" height="6" rx="1"></rect>
                </svg>'
            />

            <!-- Row 2 -->
            <x-question-type-card
                title="Multiple True/False"
                description="Students evaluate multiple statements as either true or false."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <path d="M9 11l3 3L22 4"></path>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                </svg>'
            />

            <x-question-type-card
                title="Word Matching"
                description="Students match words or phrases with their corresponding pairs."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <line x1="18" y1="4" x2="18" y2="20"></line>
                    <line x1="6" y1="4" x2="6" y2="20"></line>
                    <line x1="12" y1="4" x2="12" y2="20"></line>
                    <line x1="4" y1="12" x2="20" y2="12"></line>
                </svg>'
            />

            <x-question-type-card
                title="Drag and Drop Images"
                description="Students drag images to their correct positions or categories."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <path d="M8 18L12 22L16 18"></path>
                    <path d="M12 2V22"></path>
                </svg>'
            />

            <!-- Row 3 -->
            <x-question-type-card
                title="Dropdown Selection"
                description="Students select the correct option from dropdown menus within text."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <path d="m6 9 6 6 6-6"></path>
                    <rect x="3" y="4" width="18" height="16" rx="2"></rect>
                </svg>'
            />

            <x-question-type-card
                title="Multiple Choice Image"
                description="Students select the correct image from multiple options."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <rect x="3" y="3" width="18" height="18" rx="2"></rect>
                    <circle cx="8.5" cy="8.5" r="1.5"></circle>
                    <path d="M20.4 14.5 16 10 4 20"></path>
                </svg>'
            />

            <x-question-type-card
                title="Drag to Order"
                description="Students arrange items in the correct sequence or order."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <path d="M8 3L8 21"></path>
                    <path d="M16 3L16 21"></path>
                    <path d="M3 8L21 8"></path>
                    <path d="M3 16L21 16"></path>
                </svg>'
            />

            <!-- Row 4 -->
            <x-question-type-card
                title="Drag to Choice"
                description="Students drag items to their corresponding choice or category."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <path d="M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v4"></path>
                    <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                    <path d="M2 15h10"></path>
                    <path d="m5 12-3 3 3 3"></path>
                </svg>'
            />

            <x-question-type-card
                title="Drag to Group"
                description="Students drag items to form logical groups or sets."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
                    <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
                    <line x1="6" y1="6" x2="6.01" y2="6"></line>
                    <line x1="6" y1="18" x2="6.01" y2="18"></line>
                </svg>'
            />

            <x-question-type-card
                title="Grid to Choice"
                description="Students select cells in a grid to indicate their answers."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                    <path d="M3 9h18"></path>
                    <path d="M3 15h18"></path>
                    <path d="M9 3v18"></path>
                    <path d="M15 3v18"></path>
                </svg>'
            />

            <!-- Row 5 -->
            <x-question-type-card
                title="Fill the Blank"
                description="Students type in missing words or phrases to complete sentences."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <path d="M2 12.5V6.5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-8"></path>
                    <path d="M2 12.5a2 2 0 0 0 2 2h3"></path>
                    <path d="M2 16h.01"></path>
                    <path d="M7.5 16h.01"></path>
                    <path d="M13 20h.01"></path>
                    <path d="M18 20h.01"></path>
                </svg>'
            />

            <x-question-type-card
                title="Writing"
                description="Students compose extended written responses to prompts."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-8 w-8">
                    <path d="M12 20h9"></path>
                    <path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z"></path>
                </svg>'
            />
        </div>
    </div>
</section>
