<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" @class(['dark' => ($appearance ?? 'system') == 'dark'])>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>{{ $title ?? config('app.name', 'Aussie Edu Hub') }}</title>

        <!-- Basic Meta Tags -->
        <meta name="description" content="{{ $description ?? 'Australia\'s leading online education platform for test preparation' }}">

        <!-- Open Graph / Facebook -->
        <meta property="og:type" content="{{ $ogType ?? 'website' }}">
        <meta property="og:url" content="{{ $ogUrl ?? request()->url() }}">
        <meta property="og:title" content="{{ $ogTitle ?? ($title ?? config('app.name', 'Aussie Edu Hub')) }}">
        <meta property="og:description" content="{{ $ogDescription ?? ($description ?? 'Australia\'s leading online education platform for test preparation') }}">
        @if(isset($ogImage))
            <meta property="og:image" content="{{ $ogImage }}">
            <meta property="og:image:width" content="1200">
            <meta property="og:image:height" content="630">
        @endif

        <!-- Twitter -->
        <meta property="twitter:card" content="summary_large_image">
        <meta property="twitter:url" content="{{ $ogUrl ?? request()->url() }}">
        <meta property="twitter:title" content="{{ $ogTitle ?? ($title ?? config('app.name', 'Aussie Edu Hub')) }}">
        <meta property="twitter:description" content="{{ $ogDescription ?? ($description ?? 'Australia\'s leading online education platform for test preparation') }}">
        @if(isset($ogImage))
            <meta property="twitter:image" content="{{ $ogImage }}">
        @endif

        <!-- Favicon -->
        <link rel="icon" href="{{ asset('logo.jpg') }}" type="image/svg+xml">
        <link rel="apple-touch-icon" href="{{ asset('logo.jpg') }}">

        <!-- Google Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Instrument+Sans:wght@200;300;400;500;600;700&family=Onest:wght@400;500;600;700;800&display=swap" rel="stylesheet">

        @vite(['resources/css/app.css', 'resources/js/app.js'])
        <script src="//app.lemonsqueezy.com/js/lemon.js" defer></script>
        <style>
            [x-cloak] {
                display: none;
            }
        </style>

        @production
            <!-- Google Tag Manager -->
            <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','GTM-5ZBCBKLS');</script>
            <!-- End Google Tag Manager -->
        @endproduction
    </head>
    <body class="font-sans antialiased">
        <div class="flex min-h-screen flex-col bg-background text-foreground">
            <!-- Include header component -->
            <x-header />

            <!-- Main content -->
            <main class="flex-grow">
                {{ $slot }}
            </main>

            <!-- Call to Action Section -->
            <x-cta-section />

            <!-- Include footer component -->
            <x-footer />
        </div>
    </body>
</html>
