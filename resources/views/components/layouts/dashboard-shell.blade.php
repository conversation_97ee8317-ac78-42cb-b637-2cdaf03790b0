@props([
    'noPadding' => false,
    'sidebarComponent' => null,
])
{{--
    Available slots:
    - $title: The page title (displayed as h1)
    - $headerActions: Actions to display in the same row as the title
    - $slot: The main content of the page
--}}
        <!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? config('app.name', 'Laravel') }}</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ asset('logo.jpg') }}" type="image/svg+xml">
    <link rel="apple-touch-icon" href="{{ asset('logo.jpg') }}">

    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

    <!-- Heading Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Onest:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    @filamentStyles
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <style>
        :root {
            @foreach(\Filament\Support\Colors\Color::hex('#0099fa') as $name => $value)
                   {{ "--primary-$name" }}: {{ $value }};
            @endforeach
        }
    </style>
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
</head>
<body class="font-sans antialiased">
<div x-data="{ sidebarOpen: localStorage.getItem('sidebarOpen') === 'true' }"
     x-init="$watch('sidebarOpen', value => localStorage.setItem('sidebarOpen', value))"
     class="min-h-screen bg-gray-50">
    <!-- Sidebar -->
    {{ $sidebar }}

    <!-- Main Content -->
    <div class="lg:pl-64 flex flex-col h-screen" :class="{'lg:pl-64': sidebarOpen, 'lg:pl-16': !sidebarOpen}">

        <!-- Page Content -->
        <main @class([
        "flex-1 overflow-y-auto",
        "p-4 sm:p-6" => ! $noPadding
        ])>
            @if(isset($title) && $title)
                <div class="flex items-center justify-between mb-12">
                    <h1 class="text-2xl font-semibold text-gray-900">{{ $title }}</h1>
                    @if(isset($headerActions))
                        <div class="flex items-center space-x-3">
                            {{ $headerActions }}
                        </div>
                    @endif
                </div>
            @endif
            {{ $slot }}
        </main>
    </div>
</div>

    @livewire('notifications')
    @filamentScripts
</body>
</html>
