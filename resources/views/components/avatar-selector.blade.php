@props(['selected' => null])

<div x-data="{ selected: '{{ $selected }}' }" class="space-y-4">
    <label class="block text-sm font-medium text-gray-700">Select Avatar</label>
    
    <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-4">
        @for ($i = 1; $i <= 12; $i++)
            <div 
                @click="selected = 'avatar-{{ $i }}.svg'"
                class="cursor-pointer rounded-lg p-2 transition-all"
                :class="{ 'bg-primary/10 ring-2 ring-primary': selected === 'avatar-{{ $i }}.svg' }"
            >
                <img 
                    src="{{ asset('images/avatars/avatar-' . $i . '.svg') }}" 
                    alt="Avatar {{ $i }}"
                    class="w-full h-auto"
                />
            </div>
        @endfor
    </div>
    
    <input type="hidden" name="avatar" x-model="selected" />
</div>
