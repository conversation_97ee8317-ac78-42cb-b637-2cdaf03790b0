@props(['testPack'])

<div class="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden sticky top-32">
    @if($testPack->hasTests())
        <!-- Pricing Section -->
        <div class="p-4 bg-gradient-to-br from-slate-50 to-gray-50 border-b border-gray-100">
            @if($testPack->hasActiveDiscount())
                <!-- Discount Badge -->
                <div class="inline-flex items-center px-2 py-1 bg-rose-100 text-rose-700 text-xs font-semibold rounded-full mb-2">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    {{ $testPack->getDiscountPercent() }}% OFF
                </div>

                <!-- Price Display -->
                <div class="flex items-baseline gap-2 mb-1">
                    <span class="text-4xl font-bold text-rose-600">${{ number_format($testPack->getDiscountedPrice(), 2) }}</span>
                    <span class="text-lg text-gray-500 line-through">${{ number_format($testPack->Price, 2) }}</span>
                </div>

                <p class="text-xs text-green-600 font-medium">You save ${{ number_format($testPack->getSavingsAmount(), 2) }}</p>
            @else
                <div class="text-4xl font-bold text-gray-900">${{ number_format($testPack->Price, 2) }}</div>
                <p class="text-sm text-gray-500">One-time purchase</p>
            @endif
        </div>

        <!-- Action Button -->
        <div class="p-4">
            @if($testPack->getDiscountedPrice() == 0)
                <!-- Free/100% discount button -->
                <a href="{{ route('testPacks.claim', $testPack->Id) }}" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-out flex items-center justify-center group relative overflow-hidden">
                    <!-- Animated background shimmer -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 w-1/2 h-full transform -translate-x-full group-hover:translate-x-[200%] transition-transform duration-700 ease-out"></div>

                    <!-- Icon with enhanced animation -->
                    <svg class="h-5 w-5 mr-2 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>

                    <!-- Button text -->
                    <span class="relative z-10 text-lg tracking-wide">
                        Take It Now
                    </span>

                    <!-- Pulse animation on hover -->
                    <div class="absolute inset-0 rounded-xl bg-white/10 opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-opacity duration-300"></div>
                </a>
            @else
                <!-- Regular purchase button -->
                <a href="{{ route('testPacks.checkout', $testPack->Id) }}" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-out flex items-center justify-center group relative overflow-hidden">
                    <!-- Animated background shimmer -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 w-1/2 h-full transform -translate-x-full group-hover:translate-x-[200%] transition-transform duration-700 ease-out"></div>

                    <!-- Icon with enhanced animation -->
                    <svg class="h-5 w-5 mr-2 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>

                    <!-- Button text -->
                    <span class="relative z-10 text-lg tracking-wide">
                        Buy Now
                    </span>

                    <!-- Pulse animation on hover -->
                    <div class="absolute inset-0 rounded-xl bg-white/10 opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-opacity duration-300"></div>
                </a>
            @endif
        </div>

        <!-- Features List -->
        <div class="px-4 pb-4">
            <div class="space-y-2 text-sm">
                <div class="flex items-center text-gray-600">
                    <div class="w-4 h-4 rounded-full bg-green-100 flex items-center justify-center mr-2 flex-shrink-0">
                        <svg class="w-2.5 h-2.5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <span>Instant digital access</span>
                </div>
                <div class="flex items-center text-gray-600">
                    <div class="w-4 h-4 rounded-full bg-green-100 flex items-center justify-center mr-2 flex-shrink-0">
                        <svg class="w-2.5 h-2.5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <span>12 months of access</span>
                </div>
                <div class="flex items-center text-gray-600">
                    <div class="w-4 h-4 rounded-full bg-green-100 flex items-center justify-center mr-2 flex-shrink-0">
                        <svg class="w-2.5 h-2.5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <span>30-day money back guarantee</span>
                </div>
            </div>
        </div>
    @else
        <!-- Coming Soon Section -->
        <div class="p-4 text-center">
            <div class="inline-flex items-center px-3 py-1 bg-amber-100 text-amber-700 text-sm font-medium rounded-full mb-3">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                </svg>
                Coming Soon
            </div>
            <p class="text-sm text-gray-600 mb-4">This test pack is being prepared and will be available soon.</p>

            <div class="w-full bg-gray-100 text-gray-500 font-medium py-2.5 px-4 rounded-lg flex items-center justify-center cursor-not-allowed">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                </svg>
                Not Available Yet
            </div>
        </div>
    @endif
</div>
