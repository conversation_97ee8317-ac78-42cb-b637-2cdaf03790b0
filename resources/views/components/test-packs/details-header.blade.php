@props(['testPack'])

<div class="bg-white rounded-lg p-6 mb-6 border border-gray-100">
    <div class="mb-6">
        <h2 class="text-2xl font-bold mb-4">Overview</h2>
        @if($testPack->hasTests())
            <div class="flex items-center gap-2 mb-4 flex-wrap">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-700">
                    <svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    {{ $testPack->TotalTests }} Tests
                </span>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-700">
                    <svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                    </svg>
                    {{ $testPack->getTotalQuestions() }} Questions
                </span>
            </div>
        @else
            <div class="flex items-center gap-2 mb-4 flex-wrap">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-amber-100 text-amber-800">
                    Coming Soon
                </span>
            </div>
        @endif
    </div>

    <div class="prose max-w-none mb-6">
        <p class="text-gray-600 text-base leading-relaxed">{{ $testPack->Description }}</p>
    </div>

    <div class="mt-6">
        @if($testPack->hasTests())
            <h3 class="text-lg font-bold mb-3">Tests Included ({{ $testPack->TotalTests }})</h3>
            <div class="space-y-1">
                @foreach($testPack->Tests as $test)
                    <div class="flex items-center justify-between py-2 px-3 hover:bg-gray-50 rounded-lg border border-gray-100 transition-colors">
                        <div class="flex items-center gap-3 flex-1 min-w-0">
                            <div class="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary">
                                    <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/>
                                    <path d="m9 12 2 2 4-4"/>
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <span class="font-medium text-gray-900 truncate block">{{ $test->Title }}</span>
                            </div>
                        </div>
                        <div class="flex items-center gap-4 text-xs text-gray-500 flex-shrink-0">
                            <span class="whitespace-nowrap">{{ $test->NumberOfQuestions }} questions</span>
                            <span class="whitespace-nowrap flex items-center gap-1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400">
                                    <circle cx="12" cy="12" r="10"/>
                                    <polyline points="12,6 12,12 16,14"/>
                                </svg>
                                {{ $test->TimeInMinutes }} min
                            </span>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="bg-amber-50 border border-amber-200 rounded-lg p-6 text-center">
                <div class="mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-12 w-12 text-amber-600 mx-auto">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="m9 12 2 2 4-4"/>
                    </svg>
                </div>
                <h3 class="text-lg font-bold mb-2 text-amber-800">Coming Soon</h3>
                <p class="text-amber-700">This test pack is currently being prepared by our educational experts. Tests will be available soon!</p>
            </div>
        @endif
    </div>
</div>
