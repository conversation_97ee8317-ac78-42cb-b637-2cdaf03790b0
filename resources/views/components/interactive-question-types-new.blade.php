<section class="py-12 px-6 bg-gray-50">
    <div class="container mx-auto max-w-7xl">
        <h2 class="text-3xl font-bold mb-8 text-center text-primary">Interactive Question Types</h2>
        <p class="text-center mb-10">AussieHub supports a wide range of interactive question formats designed
            specifically for Australian students:</p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Row 1 -->
            <x-question-type-card 
                title="Multiple Choice"
                description="Traditional format where students select one correct answer from several options."
                example="Which of the following is Australia's capital city?"
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <path d="M8 6h13"></path>
                    <path d="M8 12h13"></path>
                    <path d="M8 18h13"></path>
                    <path d="M3 6h.01"></path>
                    <path d="M3 12h.01"></path>
                    <path d="M3 18h.01"></path>
                </svg>'
            />

            <x-question-type-card 
                title="Sentence Selection"
                description="Students click on specific words within a sentence to select their answers. The sentence contains multiple highlighted words that can be selected."
                example="Click on all the adjectives in this sentence: 'The quick brown fox jumps over the lazy dog.'"
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                    <path d="M20 8v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h8z"></path>
                </svg>'
            />

            <x-question-type-card 
                title="Selection Multiple"
                description="Students select multiple correct answers from a set of options."
                example="Select all Australian native animals from the list."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <path d="M2 17 17 2"></path>
                    <path d="M2 7 7 2"></path>
                    <path d="m16 16 6-6"></path>
                    <path d="m7 12 5 5"></path>
                    <path d="m12 7 5 5"></path>
                </svg>'
            />

            <!-- Row 2 -->
            <x-question-type-card 
                title="Multiple True/False"
                description="Students evaluate multiple statements as either true or false."
                example="Mark each statement about Australian geography as True or False."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                    <path d="M20 8v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h8z"></path>
                    <path d="m9 15 2 2 4-4"></path>
                </svg>'
            />

            <x-question-type-card 
                title="Word Matching"
                description="Students match words or phrases with their corresponding pairs."
                example="Match each Australian state with its capital city."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <path d="M14 16H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v2"></path>
                    <path d="M10 16v4"></path>
                    <path d="M2 10h20"></path>
                    <path d="m17 13 5 5"></path>
                    <path d="m22 13-5 5"></path>
                </svg>'
            />

            <x-question-type-card 
                title="Drag and Drop Images"
                description="Students drag images to their correct positions or categories."
                example="Drag each animal to its correct habitat."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <path d="M5 9l14 0"></path>
                    <path d="M5 15l14 0"></path>
                </svg>'
            />

            <!-- Row 3 -->
            <x-question-type-card 
                title="Dropdown Selection"
                description="Students select the correct option from dropdown menus within text."
                example="Complete the sentence by selecting the correct word from each dropdown."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <path d="m6 9 6 6 6-6"></path>
                </svg>'
            />

            <x-question-type-card 
                title="Multiple Choice Image"
                description="Students select the correct image from multiple options."
                example="Which image shows the correct solution to this math problem?"
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7"></path>
                    <path d="M18 15v-6h-6"></path>
                    <path d="M15 12 9 6"></path>
                    <path d="M9 15v-4"></path>
                    <path d="M6 12h3"></path>
                </svg>'
            />

            <x-question-type-card 
                title="Drag to Order"
                description="Students arrange items in the correct sequence or order."
                example="Arrange these events in Australian history in chronological order."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <rect width="6" height="16" x="4" y="4" rx="1"></rect>
                    <rect width="6" height="16" x="14" y="4" rx="1"></rect>
                </svg>'
            />

            <!-- Row 4 -->
            <x-question-type-card 
                title="Drag to Choice"
                description="Students drag items to their corresponding choice or category."
                example="Drag each term to either 'Noun', 'Verb', or 'Adjective' category."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <path d="M5 9l14 0"></path>
                    <path d="M5 15l14 0"></path>
                </svg>'
            />

            <x-question-type-card 
                title="Drag to Group"
                description="Students drag items to form logical groups or sets."
                example="Group these Australian animals by their habitat type."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <path d="M17 18a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v9z"></path>
                    <path d="M9 9h6"></path>
                    <path d="M9 13h6"></path>
                    <path d="M9 17h6"></path>
                    <path d="M13 5V3"></path>
                    <path d="M11 3h4"></path>
                </svg>'
            />

            <x-question-type-card 
                title="Grid to Choice"
                description="Students select cells in a grid to indicate their answers."
                example="Select all grid cells that contain prime numbers."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <path d="M3 3h18v18H3z"></path>
                    <path d="M3 9h18"></path>
                    <path d="M3 15h18"></path>
                    <path d="M9 3v18"></path>
                    <path d="M15 3v18"></path>
                </svg>'
            />

            <!-- Row 5 -->
            <x-question-type-card 
                title="Fill the Blank"
                description="Students type in missing words or phrases to complete sentences."
                example="Canberra became Australia's capital city in _____."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"></path>
                </svg>'
            />

            <x-question-type-card 
                title="Writing"
                description="Students compose extended written responses to prompts."
                example="Write a persuasive essay about the importance of conservation in Australia."
                icon='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="h-12 w-12">
                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3Z"></path>
                </svg>'
            />
        </div>
    </div>
</section>
