@props(['posts' => []])

<section {{ $attributes->class("py-16") }}>
    <div class="container mx-auto max-w-7xl px-6">
        <!-- Section Header -->
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Latest Educational Insights
            </h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Stay updated with the latest tips, resources, and insights to help your child succeed in their educational journey
            </p>
        </div>

        @if(!empty($posts))
            <!-- Blog Posts Slider -->
            <div class="relative overflow-hidden py-4 mb-12">
                <div class="slider-container flex transition-transform duration-500 ease-in-out">
                    @foreach($posts as $index => $post)
                        <div class="slide min-w-1/3 px-2">
                            <x-blog-post-card :post="$post" />
                        </div>
                    @endforeach
                </div>

                <!-- Navigation Arrows -->
                <button class="slider-prev absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 rounded-full p-2 shadow-lg transition-all duration-300 z-10">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <button class="slider-next absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 rounded-full p-2 shadow-lg transition-all duration-300 z-10">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>

            <!-- View All Posts Link -->
            <div class="text-center">
                <a
                    href="{{ route('blog.index') }}"
                    class="inline-flex items-center gap-1 text-primary hover:text-primary/80 font-medium transition-colors group"
                >
                    View All Posts
                    <svg class="h-4 w-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        @else
            <!-- No Posts Available -->
            <div class="text-center py-12">
                <div class="max-w-md mx-auto">
                    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Coming Soon</h3>
                    <p class="text-gray-600">
                        We're working on creating valuable educational content for you. Check back soon!
                    </p>
                </div>
            </div>
        @endif
    </div>
</section>

<style>
    .slider-container {
        transform: translateX(0);
        align-items: stretch;
    }

    .slide {
        flex: 0 0 auto;
        width: 33.333333%;
        display: flex;
    }

    .slide > * {
        flex: 1;
        height: 100%;
    }

    /* Mobile optimizations */
    @media (max-width: 767px) {
        .slide {
            width: 100%; /* Show 1 card on mobile */
            padding: 0 8px;
        }

        .slider-prev {
            left: 8px !important;
            padding: 8px !important;
        }

        .slider-next {
            right: 8px !important;
            padding: 8px !important;
        }
    }

    @media (min-width: 768px) and (max-width: 1023px) {
        .slide {
            width: 50%; /* Show 2 cards on tablet */
        }
    }

    .slider-prev:hover,
    .slider-next:hover {
        transform: translateY(-50%) scale(1.05);
    }

    /* Auto-slide animation */
    @keyframes slide-auto {
        0% { transform: translateX(0); }
        33% { transform: translateX(-100%); }
        66% { transform: translateX(-200%); }
        100% { transform: translateX(0); }
    }

    .auto-slide {
        animation: slide-auto 12s infinite;
    }

    /* Pause animation on hover */
    .slider-container:hover.auto-slide {
        animation-play-state: paused;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sliderContainer = document.querySelector('.slider-container');
    const slides = document.querySelectorAll('.slide');
    const prevBtn = document.querySelector('.slider-prev');
    const nextBtn = document.querySelector('.slider-next');

    if (!sliderContainer || slides.length === 0) return;

    let currentSlide = 0;
    const totalSlides = slides.length;
    let slideWidth = 33.333333; // Default for desktop

    function updateSlideWidth() {
        if (window.innerWidth < 768) {
            slideWidth = 100; // 1 slide on mobile
        } else if (window.innerWidth < 1024) {
            slideWidth = 50; // 2 slides on tablet
        } else {
            slideWidth = 33.333333; // 3 slides on desktop
        }
    }

    function updateSlider() {
        updateSlideWidth();
        sliderContainer.style.transform = `translateX(-${currentSlide * slideWidth}%)`;
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % totalSlides;
        updateSlider();
    }

    function prevSlide() {
        currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
        updateSlider();
    }

    // Event listeners
    if (nextBtn) nextBtn.addEventListener('click', nextSlide);
    if (prevBtn) prevBtn.addEventListener('click', prevSlide);

    // Touch events for mobile
    let touchStartX = 0;
    let touchEndX = 0;

    sliderContainer.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
    });

    sliderContainer.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    });

    function handleSwipe() {
        const swipeThreshold = 50; // Minimum distance for a swipe
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                nextSlide(); // Swipe left - next slide
            } else {
                prevSlide(); // Swipe right - previous slide
            }
        }
    }

    // Auto-slide functionality
    let autoSlideInterval = setInterval(nextSlide, 4000);

    // Pause auto-slide on hover
    sliderContainer.addEventListener('mouseenter', () => {
        clearInterval(autoSlideInterval);
    });

    sliderContainer.addEventListener('mouseleave', () => {
        autoSlideInterval = setInterval(nextSlide, 4000);
    });

    // Handle window resize
    window.addEventListener('resize', updateSlider);

    // Initialize
    updateSlider();
});
</script>
