<section class="relative overflow-hidden py-20 lg:py-24">
    <!-- Background with gradient overlay -->
    <div class="absolute inset-0 bg-gradient-to-r from-primary/90 to-primary/70 z-0"></div>

    <!-- CSS-only geometric pattern for better performance -->
    <div class="absolute inset-0 opacity-5 z-0" style="background: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2px, transparent 2px); background-size: 50px 50px;"></div>

    <!-- Floating shapes for visual interest -->
    <div class="absolute top-20 left-10 w-64 h-64 bg-white/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-10 right-10 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>

    <div class="container relative z-10 mx-auto max-w-7xl px-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Left content -->
            <div class="text-white">
                <h1 class="text-4xl sm:text-5xl lg:text-5xl font-bold mb-6 leading-tight" style="font-display: swap;">
                    <span class="text-white">Master Your</span>
                    <span class="block text-yellow-300 mt-2">NAPLAN Tests</span>
                </h1>

                <div class="text-lg lg:text-2xl font-medium mb-6 text-white/90">Smart NAPLAN Practice & Preparation Platform</div>

                <!-- Key Features -->
                <div class="space-y-4 mb-8">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center mt-1">
                            <svg class="w-3 h-3 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <p class="text-white/95 font-medium"><strong>World's First AI Writing Marker</strong> - Advanced AI technology that marks your writing tests just like real examiners</p>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center mt-1">
                            <svg class="w-3 h-3 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <p class="text-white/95 font-medium"><strong>Most Realistic Practice Tests</strong> - Academic tests designed to mirror real exam conditions perfectly</p>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center mt-1">
                            <svg class="w-3 h-3 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <p class="text-white/95 font-medium"><strong>Now on iPhone & iPad</strong> - Your child can practice anywhere with our new mobile app</p>
                    </div>
                </div>

                <div class="flex flex-col items-start gap-4">
                    <a href="{{ route('test-packs.index') }}" class="inline-block rounded-full bg-white text-primary px-8 py-4 font-medium shadow-lg transition-all duration-500 ease-in-out hover:bg-white/90 group">
                        <span class="inline-flex items-center">
                            Start Learning Now
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transition-transform duration-500 ease-in-out transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </span>
                    </a>

                    <!-- App Store & Google Play Badges -->
                    <div class="flex items-center gap-3 mt-6">
                        <!-- App Store Badge -->
                        <a href="https://apps.apple.com/au/app/aeh-student-portal/id6746640734" target="_blank" class="inline-flex items-center bg-black text-white px-4 py-3 rounded-lg hover:bg-gray-800 transition-colors duration-300 w-40 h-12">
                            <svg class="w-6 h-6 mr-2" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M18.71 19.5C17.88 20.74 17 21.95 15.66 21.97C14.32 22 13.89 21.18 12.37 21.18C10.84 21.18 10.37 21.95 9.09998 22C7.78998 22.05 6.79998 20.68 5.95998 19.45C4.24998 17 2.93998 12.45 4.69998 9.39C5.56998 7.87 7.13998 6.91 8.81998 6.88C10.1 6.84 11.32 7.75 12.11 7.75C12.89 7.75 14.37 6.68 15.92 6.84C16.57 6.87 18.39 7.1 19.56 8.82C19.47 8.88 17.39 10.19 17.41 12.63C17.44 15.65 20.06 16.66 20.09 16.67C20.06 16.74 19.67 18.11 18.71 19.5ZM13 3.5C13.73 2.67 14.94 2.04 15.94 2C16.07 3.17 15.6 4.35 14.9 5.19C14.21 6.04 13.07 6.7 11.95 6.61C11.8 5.46 12.36 4.26 13 3.5Z"/>
                            </svg>
                            <div class="text-left">
                                <div class="text-xs">Download on the</div>
                                <div class="text-sm font-semibold">App Store</div>
                            </div>
                        </a>

                        <!-- Google Play Store Badge (No Link) -->
                        <div class="relative inline-flex items-center bg-black text-white px-4 py-3 rounded-lg hover:bg-gray-800 transition-colors duration-300 w-40 h-12"
                        onclick="return;"
                        >
                            <!-- Coming Soon Label -->
                            <div class="absolute -top-2 -right-8 bg-white/90 text-black text-xs px-2 py-0.5 rounded-full shadow-sm">
                                Coming Soon
                            </div>

                            <svg class="w-6 h-6 mr-2" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                            </svg>
                            <div class="text-left">
                                <div class="text-xs">GET IT ON</div>
                                <div class="text-sm font-semibold">Google Play</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right content with floating image -->
            <div class="relative">
                <div class="absolute -top-6 -left-6 w-full h-full bg-white/10 rounded-2xl rotate-3 backdrop-blur-sm border border-white/20"></div>
                <div class="absolute -bottom-6 -right-6 w-full h-full bg-white/10 rounded-2xl -rotate-3 backdrop-blur-sm border border-white/20"></div>

                <div class="relative bg-white p-3 rounded-2xl shadow-2xl transform hover:scale-[1.02] transition-transform duration-500">
                    <img
                        src="/images/illustrations/studying.svg"
                        alt="Education Platform"
                        class="w-full h-auto rounded-xl"
                        loading="lazy"
                    >

                    <!-- Stats overlay -->
                    <div class="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-white rounded-xl shadow-xl p-4 flex gap-6 w-5/6 justify-center">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-primary">AI</p>
                            <p class="text-xs text-gray-600">Writing Marker</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-primary">1000+</p>
                            <p class="text-xs text-gray-600">Practice Tests</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-primary">iOS</p>
                            <p class="text-xs text-gray-600">App Ready</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-primary">24/7</p>
                            <p class="text-xs text-gray-600">Support</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Wave shape at bottom -->
    <div class="absolute bottom-0 left-0 right-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" class="w-full h-auto">
            <path fill="transparent" fill-opacity="1" d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
        </svg>
    </div>
</section>


