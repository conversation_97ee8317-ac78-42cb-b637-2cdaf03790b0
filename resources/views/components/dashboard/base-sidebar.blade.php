@props([])

<aside
    x-cloak
    class="fixed inset-y-0 left-0 z-50 bg-white border-r border-gray-200 transition-all duration-300"
    :class="{'w-64': sidebarOpen, 'w-16': !sidebarOpen, '-translate-x-full lg:translate-x-0': !sidebarOpen, 'translate-x-0': sidebarOpen}"
>
    <!-- Sidebar header -->
    <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
        <a href="{{ route('home') }}" class="flex items-center space-x-3">
            <div x-show="sidebarOpen">
                <x-app-logo class="text-lg" />
            </div>
        </a>

        <!-- Toggle button for desktop -->
        <button
            @click="sidebarOpen = !sidebarOpen"
            class="hidden lg:block p-1 rounded-md hover:bg-gray-100"
        >
            <x-heroicon-o-bars-3-bottom-left class="size-5 text-gray-500" />
        </button>

        <!-- Close button for mobile -->
        <button
            @click="sidebarOpen = false"
            class="lg:hidden p-1 rounded-md hover:bg-gray-100"
        >
            <x-heroicon-o-x-mark class="h-5 w-5 text-gray-500" />
        </button>
    </div>

    <!-- Sidebar content -->
    <div class="py-4 overflow-hidden">
        <nav class="space-y-2 px-2 pt-4">
            {{ $slot }}
        </nav>
    </div>

    <!-- Sidebar footer -->
    <div class="absolute bottom-0 w-full border-t border-gray-200">
        <div class="p-4">
            {{ $footer ?? '' }}
        </div>
    </div>
</aside>

<!-- Mobile sidebar backdrop -->
<div
    x-cloak
    x-show="sidebarOpen"
    @click="sidebarOpen = false"
    class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
></div>

<!-- Mobile sidebar toggle button -->
<div class="fixed top-0-6 left-0 mt-12 -ml-2 z-30 lg:hidden">
    <button
        @click="sidebarOpen = true"
        class="p-2 rounded-md bg-white/80 backdrop-blur-sm text-gray-700 shadow-md border border-gray-200/70 hover:bg-white transition-colors"
    >
        <x-heroicon-o-bars-3-bottom-left class="size-4" />
    </button>
</div>
