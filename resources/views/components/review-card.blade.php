@props([
    'quote',
    'author',
    'position' => null,
    'avatar' => null,
    'background' => 'bg-white',
    'textColor' => 'text-gray-800',
    'size' => 'normal' // normal, large
])

<div {{ $attributes->merge(['class' => "{$background} {$textColor} p-6 rounded-lg h-full flex flex-col" . ($background === 'bg-white' ? ' border border-gray-200' : ' border border-transparent')]) }}>
    <div class="flex-grow">
        <p class="text-base mb-6 leading-relaxed">
            "{{ $quote }}"
        </p>
    </div>
    <div class="flex items-center gap-3 mt-auto">
        @if($avatar)
            <img src="{{ $avatar }}" alt="{{ $author }}" class="w-12 h-12 rounded-full object-cover">
        @else
            <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold text-lg">
                {{ substr($author, 0, 1) }}
            </div>
        @endif
        <div>
            <h4 class="font-bold">{{ $author }}</h4>
            @if($position)
                <p class="text-sm opacity-75">{{ $position }}</p>
            @endif
        </div>
    </div>
</div>
