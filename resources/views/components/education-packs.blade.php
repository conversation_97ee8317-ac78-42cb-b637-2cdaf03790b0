@props(['testPacks'])

<section class="py-20 px-6 bg-gradient-to-b from-secondary/5 to-secondary/20">
    <div class="container mx-auto max-w-7xl">
        <h2 class="text-3xl font-bold mb-4 text-center text-primary">Shop Test Packs</h2>
        <p class="text-center mb-12 max-w-3xl mx-auto text-gray-600">
            Browse our comprehensive collection of educational materials designed to help students excel in their
            academic journey.
        </p>

        <!-- All Educational Packs -->
        <div>
            <div class="flex items-center gap-3 mb-8">
                <div class="bg-primary/5 w-10 h-10 rounded-lg flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                         stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                         class="h-5 w-5 text-primary">
                        <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold">All Packs</h3>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach(collect($testPacks)->take(6) as $pack)
                    <x-test-pack-card :pack="$pack" />
                @endforeach
            </div>

            <!-- View all link -->
            <div class="flex justify-center mt-12">
                <a href="{{ route('test-packs.index') }}"
                   class="inline-flex items-center gap-2 text-primary font-medium hover:text-primary/80 hover:underline">
                    Shop All Test Packs
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                         stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                         class="w-4 h-4">
                        <path d="M5 12h14"></path>
                        <path d="m12 5 7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        </div>
    </div>
</section>
