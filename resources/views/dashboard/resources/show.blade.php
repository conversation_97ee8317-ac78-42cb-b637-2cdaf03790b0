<x-layouts.dashboard>
    <x-slot name="title">{{ $resource['name'] ?? 'Resource Details' }}</x-slot>

    <x-slot name="headerActions">
        <a href="{{ route('dashboard.resources.index') }}"
           class="px-5 py-2 bg-gray-100 text-gray-700 flex items-center text-sm font-medium rounded-md hover:bg-gray-200 transition-colors">
            <x-heroicon-o-arrow-left class="h-5 w-5 mr-2" />
            Back to Resources
        </a>
    </x-slot>

    <div class="bg-white rounded-lg shadow-md p-6">
        <!-- Resource Information -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Resource Information</h2>
            
            @if(!empty($resource))
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $resource['name'] }}</h3>
                        @if(!empty($resource['short_description']))
                            <p class="text-gray-600 mb-4">{!! strip_tags($resource['short_description']) !!}</p>
                        @endif
                        
                        <div class="text-sm text-gray-500">
                            <p><strong>Purchased:</strong> {{ $boughtResource->created_at->format('M j, Y') }}</p>
                            <p><strong>Files Available:</strong> {{ count($downloadFiles) }}</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Download Files -->
        @if(!empty($downloadFiles))
            <div class="border-t pt-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Download Files</h2>
                
                <div class="grid gap-4">
                    @foreach($downloadFiles as $file)
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-4">
                                    <x-heroicon-o-document-arrow-down class="w-5 h-5 text-primary" />
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900">{{ $file['name'] }}</h3>
                                    <p class="text-sm text-gray-500">Digital Download</p>
                                </div>
                            </div>
                            <a href="{{ $file['file'] }}" 
                               target="_blank"
                               class="inline-flex items-center px-4 py-2 bg-primary text-white text-sm font-medium rounded-md hover:bg-primary/90 transition-colors">
                                <x-heroicon-o-arrow-down-tray class="w-4 h-4 mr-2" />
                                Download
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        @else
            <div class="border-t pt-8">
                <div class="text-center py-12">
                    <x-heroicon-o-document-arrow-down class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No files available</h3>
                    <p class="text-gray-500">This resource doesn't have any downloadable files at the moment.</p>
                </div>
            </div>
        @endif

        <!-- Important Notice -->
        <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="flex">
                <x-heroicon-o-information-circle class="w-5 h-5 text-blue-400 mr-3 mt-0.5" />
                <div>
                    <h3 class="text-sm font-medium text-blue-800">Important</h3>
                    <p class="text-sm text-blue-700 mt-1">
                        Please save these download links in a safe place. You can access your purchased resources anytime from this dashboard.
                    </p>
                </div>
            </div>
        </div>
    </div>
</x-layouts.dashboard>
