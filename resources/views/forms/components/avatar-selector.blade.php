@php
    $statePath = $getStatePath();
    $id = $getId();
@endphp

<x-dynamic-component
    :component="$getFieldWrapperView()"
    :id="$id"
    :label="$getLabel()"
    :label-sr-only="$isLabelHidden()"
    :helper-text="$getHelperText()"
    :hint="$getHint()"
    :hint-icon="$getHintIcon()"
    :required="$isRequired()"
    :state-path="$statePath"
>
    <div
        x-data="{
            state: $wire.entangle('{{ $statePath }}'),
        }"
        class="space-y-4"
    >
        <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-4">
            @for ($i = 1; $i <= 12; $i++)
                <div
                    @click="state = 'avatar-{{ $i }}.png'"
                    class="cursor-pointer rounded-lg p-2 transition-all"
                    :class="{ 'bg-primary/10 ring-2 ring-primary': state === 'avatar-{{ $i }}.png' }"
                >
                    <img
                        src="{{ asset('images/avatars/avatar-' . $i . '.png') }}"
                        alt="Avatar {{ $i }}"
                        class="w-full h-auto rounded-full"
                    />
                </div>
            @endfor
        </div>
    </div>
</x-dynamic-component>
