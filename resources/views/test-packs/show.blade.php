<x-layouts.app>
    {{-- Hero section with colored background based on test pack --}}
    <div class="py-12 px-6" style="background-color: {{ $testPack->getBackgroundColor() }}">
        <div class="container mx-auto max-w-7xl">
            <div class="mb-4">
                <a href="{{ route('test-packs.index') }}" class="inline-flex items-center text-white hover:text-white/90 text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-1">
                        <path d="m12 19-7-7 7-7"/>
                        <path d="M19 12H5"/>
                    </svg>
                    Back to Shop
                </a>
            </div>

            <div class="flex flex-col md:flex-row items-start gap-6">
                <div class="bg-white rounded-lg p-6 border border-gray-100 w-20 h-20 flex items-center justify-center flex-shrink-0">
                    <span class="text-3xl font-bold" style="color: {{ $testPack->getBackgroundColor() }}">
                        {{ $testPack->getBadgeAbbreviation() }}
                    </span>
                </div>
                <div>
                    <h1 class="text-3xl sm:text-4xl font-bold mb-2 text-white">{{ $testPack->Title }}</h1>
                    <div class="flex items-center gap-2 flex-wrap">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/20 text-white">
                            Year {{ $testPack->Level }}
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/20 text-white">
                            {{ $testPack->Category }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Main content --}}
    <main class="container mx-auto max-w-7xl py-12 px-6">
        {{-- Flash messages --}}
        @if(session('error'))
            <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-red-600 mr-2">
                        <circle cx="12" cy="12" r="10"/>
                        <line x1="15" y1="9" x2="9" y2="15"/>
                        <line x1="9" y1="9" x2="15" y2="15"/>
                    </svg>
                    <p class="text-red-800 font-medium">{{ session('error') }}</p>
                </div>
            </div>
        @endif

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            {{-- Pack details - left side --}}
            <div class="md:col-span-2">
                <x-test-packs.details-header :testPack="$testPack" />
            </div>

            {{-- Purchase options - right side --}}
            <div class="md:col-span-1">
                <x-test-packs.purchase-sidebar :testPack="$testPack" />
            </div>
        </div>
    </main>

    {{-- Reviews section --}}
    <section class="bg-gray-50 py-12 px-6">
        <div class="container mx-auto max-w-7xl">
            <x-test-packs.student-reviews :testPack="$testPack" />
        </div>
    </section>

    <x-test-packs.related-packs :relatedTestPacks="$relatedTestPacks" />
</x-layouts.app>
