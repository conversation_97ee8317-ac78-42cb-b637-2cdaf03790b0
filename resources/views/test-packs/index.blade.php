<x-layouts.app>
    {{-- Hero Banner --}}
    <div class="bg-gradient-to-r from-primary to-primary/80 text-white py-12 px-6">
        <div class="container mx-auto max-w-7xl">
            <div class="max-w-3xl mx-auto text-center">
                <h1 class="text-3xl sm:text-4xl font-bold mb-4">Educational Test Packs</h1>
                <p class="text-lg text-white/90">Comprehensive resources designed to help students excel in their academic journey</p>
            </div>
        </div>
    </div>

    {{-- Main content --}}
    <main class="container mx-auto max-w-7xl py-12 px-6"
        x-data="{
            selectedCategory: '',
            selectedYear: ''
        }">

        {{-- Filter Section --}}
        <div class="pb-6 mb-10 border-b border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {{-- Category Filter --}}
                <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-3">Filter by Category</h3>
                    <div class="flex flex-wrap gap-2">
                        <button
                            @click="selectedCategory = ''"
                            :class="selectedCategory === '' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                            class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200">
                            All Categories
                        </button>
                        @foreach($categories as $key => $label)
                            <button
                                @click="selectedCategory = '{{ $key }}'"
                                :class="selectedCategory === '{{ $key }}' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                                class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200">
                                {{ $label }}
                            </button>
                        @endforeach
                    </div>
                </div>

                {{-- Year Level Filter --}}
                <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-3">Filter by Year Level</h3>
                    <div class="flex flex-wrap gap-2">
                        <button
                            @click="selectedYear = ''"
                            :class="selectedYear === '' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                            class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200">
                            All Years
                        </button>
                        @foreach($years as $year)
                            <button
                                @click="selectedYear = '{{ $year }}'"
                                :class="selectedYear === '{{ $year }}' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                                class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200">
                                Year {{ $year }}
                            </button>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        {{-- Test packs grid --}}
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($testPacks as $pack)
                <div
                    class="test-pack-item h-full"
                    x-show="
                    (!selectedCategory || selectedCategory === '{{ $pack->Category }}') &&
                    (!selectedYear || selectedYear === '{{ $pack->Level }}')
                "
                    x-transition:enter="transition ease-out duration-300"
                    x-transition:enter-start="opacity-0 transform scale-90"
                    x-transition:enter-end="opacity-100 transform scale-100">
                    <x-test-pack-card :pack="$pack" />
                </div>
            @endforeach
        </div>

        {{-- No results message --}}
        <div x-show="document.querySelectorAll('.test-pack-item:not([style*=\'display: none\'])').length === 0" class="text-center py-12 bg-gray-50 rounded-lg mt-6">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No test packs found</h3>
            <p class="text-gray-600">Try adjusting your filters or search criteria</p>
        </div>


    </main>

    {{-- Features Section --}}
    <section class="bg-gray-50 py-12 px-6">
        <div class="container mx-auto max-w-7xl">
            <div class="text-center mb-8">
                <h2 class="text-2xl font-bold mb-2">Why Choose Our Test Packs</h2>
                <p class="text-gray-600 max-w-3xl mx-auto">Our comprehensive test packs are designed to help students excel in their academic journey</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
                    <h3 class="text-lg font-bold mb-2 text-primary">Curriculum Aligned</h3>
                    <p class="text-gray-600 text-sm">All our test packs are carefully aligned with the Australian curriculum to ensure relevant and effective practice.</p>
                </div>

                <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
                    <h3 class="text-lg font-bold mb-2 text-primary">Comprehensive Coverage</h3>
                    <p class="text-gray-600 text-sm">Our test packs cover all key subject areas with detailed explanations and varied question types.</p>
                </div>

                <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
                    <h3 class="text-lg font-bold mb-2 text-primary">Expert Developed</h3>
                    <p class="text-gray-600 text-sm">Created by experienced educators and subject matter experts to ensure high-quality learning materials.</p>
                </div>
            </div>
        </div>
    </section>
</x-layouts.app>
