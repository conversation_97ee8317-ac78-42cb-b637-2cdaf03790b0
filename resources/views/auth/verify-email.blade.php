<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>Verify Email - {{ config('app.name', 'Laravel') }}</title>

    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

    <!-- Heading Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Onest:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-primary">
    <div class="min-h-screen flex flex-col items-center justify-center relative overflow-hidden">
        <!-- Background elements -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary to-primary/80 z-0"></div>
        <div class="absolute top-0 right-0 w-40 h-40 bg-white/10 rounded-full -translate-y-1/2 translate-x-1/2"></div>
        <div class="absolute bottom-0 left-0 w-64 h-64 bg-white/10 rounded-full translate-y-1/2 -translate-x-1/2"></div>

        <!-- Content container -->
        <div class="relative z-10 w-full max-w-lg px-6 py-12">
            <!-- Logo -->
            <div class="text-center mb-10">
                <div class="flex justify-center">
                    <x-app-logo class="text-white text-3xl mb-4" />
                </div>
                <p class="text-white/90 text-sm mb-8">Verify your email address to access your dashboard</p>
            </div>

            <!-- Email Verification Form -->
            <div class="bg-white shadow-lg rounded-xl p-10">
                <div class="text-center mb-8">
                    <div class="flex justify-center mb-4">
                        <svg class="h-16 w-16 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900">Check Your Email</h2>
                    <p class="text-sm text-gray-600 mt-2">We've sent a verification link to your email address</p>
                </div>

                <!-- Status Message -->
                @if (session('status') == 'verification-link-sent')
                    <div class="mb-6 p-4 rounded-lg bg-green-50 text-green-700">
                        <div class="flex">
                            <svg class="h-5 w-5 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="font-medium">A new verification link has been sent to your email address.</span>
                        </div>
                    </div>
                @endif

                <div class="text-center space-y-6">
                    <div class="text-sm text-gray-600">
                        <p class="mb-2">Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you?</p>
                        <p>If you didn't receive the email, we will gladly send you another.</p>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-3 justify-center">
                        <!-- Resend Verification Email -->
                        <form method="POST" action="{{ route('verification.send') }}">
                            @csrf
                            <button type="submit" class="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 bg-primary border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary/90 focus:bg-primary/90 active:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                Resend Verification Email
                            </button>
                        </form>

                        <!-- Logout -->
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150">
                                Log Out
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="mt-8 text-center text-sm text-white/80">
                <p>&copy; {{ date('Y') }} {{ config('app.name') }}. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>
