<x-layouts.app>

    {{-- Main content --}}
    <main class="container mx-auto max-w-7xl py-6 px-6">
        <div class="bg-white rounded-lg p-8">
            <h1 class="text-3xl font-bold mb-8">Frequently Asked Questions</h1>

            <div class="space-y-6" x-data="{
                activeTab: 'general',
                activeQuestion: null,
                toggleQuestion(id) {
                    this.activeQuestion = this.activeQuestion === id ? null : id;
                }
            }">
                {{-- FAQ Categories --}}
                <div class="flex flex-wrap gap-2 mb-8">
                    <button @click="activeTab = 'general'" :class="activeTab === 'general' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'" class="px-4 py-2 rounded-full text-sm font-medium transition-colors">
                        General
                    </button>
                    <button @click="activeTab = 'account'" :class="activeTab === 'account' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'" class="px-4 py-2 rounded-full text-sm font-medium transition-colors">
                        Account & Registration
                    </button>
                    <button @click="activeTab = 'testpacks'" :class="activeTab === 'testpacks' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'" class="px-4 py-2 rounded-full text-sm font-medium transition-colors">
                        Test Packs
                    </button>
                    <button @click="activeTab = 'payment'" :class="activeTab === 'payment' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'" class="px-4 py-2 rounded-full text-sm font-medium transition-colors">
                        Payment & Billing
                    </button>
                    <button @click="activeTab = 'technical'" :class="activeTab === 'technical' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'" class="px-4 py-2 rounded-full text-sm font-medium transition-colors">
                        Technical Support
                    </button>
                </div>

                {{-- General FAQs --}}
                <div x-show="activeTab === 'general'" class="space-y-4">
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('general-1')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>What is {{ config('app.name') }}?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'general-1' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'general-1'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>{{ config('app.name') }} is Australia's leading online education platform designed to help students prepare for various educational assessments, including NAPLAN, Opportunity Class, and Selective High School exams. We provide high-quality practice tests, learning resources, and educational materials to support students in achieving their academic goals.</p>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('general-2')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>Who can benefit from using {{ config('app.name') }}?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'general-2' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'general-2'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>Our platform is designed for students of all ages, particularly those preparing for standardized tests in Australia. Parents, teachers, and educational institutions can also benefit from our resources to support student learning and academic development.</p>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('general-3')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>How do I get started with {{ config('app.name') }}?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'general-3' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'general-3'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>Getting started is easy! Simply create an account, browse our available test packs, and select the resources that best suit your educational needs. You can register for a free trial to explore our platform before making a purchase.</p>
                        </div>
                    </div>
                </div>

                {{-- Account & Registration FAQs --}}
                <div x-show="activeTab === 'account'" class="space-y-4">
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('account-1')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>How do I create an account?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'account-1' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'account-1'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>To create an account, click on the "Register for Trial" button in the top right corner of our website. Fill out the registration form with your information, and you'll receive a confirmation email to activate your account.</p>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('account-2')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>Can I have multiple student profiles under one parent account?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'account-2' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'account-2'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>Yes! Parents can add multiple student profiles to their account. This allows you to manage educational resources for all your children in one place and track their individual progress.</p>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('account-3')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>How do I reset my password?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'account-3' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'account-3'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>If you've forgotten your password, click on the "Login" button, then select "Forgot Password." Enter your email address, and we'll send you instructions to reset your password. For security reasons, password reset links expire after 24 hours.</p>
                        </div>
                    </div>
                </div>

                {{-- Test Packs FAQs --}}
                <div x-show="activeTab === 'testpacks'" class="space-y-4">
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('testpacks-1')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>What are test packs?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'testpacks-1' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'testpacks-1'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>Test packs are collections of practice tests and educational resources designed for specific grade levels and subjects. Each pack contains multiple tests with questions that align with Australian curriculum standards and assessment formats.</p>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('testpacks-2')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>How long do I have access to a test pack after purchase?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'testpacks-2' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'testpacks-2'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>Once purchased, you have access to the test pack for 12 months. This gives students ample time to work through the materials at their own pace and revisit content as needed for reinforcement.</p>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('testpacks-3')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>Can I share test packs with others?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'testpacks-3' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'testpacks-3'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>Test packs are licensed for use by the registered account holder and their registered students only. Sharing access with others outside your account violates our terms of service. However, parents can assign test packs to multiple children within their family account.</p>
                        </div>
                    </div>
                </div>

                {{-- Payment & Billing FAQs --}}
                <div x-show="activeTab === 'payment'" class="space-y-4">
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('payment-1')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>What payment methods do you accept?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'payment-1' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'payment-1'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>We accept all major credit cards (Visa, Mastercard, American Express), PayPal, and direct bank transfers for Australian customers. All payments are processed securely through our payment gateway.</p>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('payment-2')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>Do you offer refunds?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'payment-2' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'payment-2'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>We offer a 30-day satisfaction guarantee. If you're not satisfied with your purchase, contact our customer support team within 30 days of purchase for a full refund. Please note that refunds are not available for partially used test packs or after the 30-day period.</p>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('payment-3')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>Are there any discounts available for bulk purchases?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'payment-3' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'payment-3'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>Yes, we offer special pricing for schools and educational institutions purchasing multiple licenses. Please contact our sales <NAME_EMAIL> for more information about our educational institution packages.</p>
                        </div>
                    </div>
                </div>

                {{-- Technical Support FAQs --}}
                <div x-show="activeTab === 'technical'" class="space-y-4">
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('technical-1')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>What browsers are supported?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'technical-1' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'technical-1'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>Our platform works best with the latest versions of Chrome, Firefox, Safari, and Edge. We recommend keeping your browser updated for the best experience. Internet Explorer is not supported.</p>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('technical-2')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>Can I use {{ config('app.name') }} on mobile devices?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'technical-2' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'technical-2'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>Yes, our website is fully responsive and works on smartphones and tablets. However, for the best testing experience, we recommend using a desktop or laptop computer, especially for longer assessments.</p>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <button @click="toggleQuestion('technical-3')" class="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
                            <span>What should I do if I encounter technical issues?</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform" :class="activeQuestion === 'technical-3' ? 'rotate-180' : ''">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                        <div x-show="activeQuestion === 'technical-3'" class="p-4 bg-gray-50 border-t border-gray-200">
                            <p>If you experience technical issues, first try refreshing the page or clearing your browser cache. If problems persist, please contact our technical support <NAME_EMAIL> with details about the issue, including your device, browser, and steps to reproduce the problem.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-12 p-6 bg-gray-50 rounded-lg text-center">
                <h2 class="text-xl font-bold mb-4">Still have questions?</h2>
                <p class="mb-6">Our support team is here to help you with any questions or concerns.</p>
                <a href="{{ route('pages.contact') }}" class="inline-flex items-center gap-2 px-6 py-3 rounded-md bg-primary text-white font-medium hover:bg-primary/90 transition-colors">
                    Contact Us
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <path d="M5 12h14"></path>
                        <path d="m12 5 7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        </div>
    </main>
</x-layouts.app>
