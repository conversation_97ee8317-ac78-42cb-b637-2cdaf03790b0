<x-layouts.app>
    <x-slot name="title">Purchase Successful</x-slot>

    <div class="container mx-auto max-w-7xl py-16 px-6 text-center">
        <div class="bg-white rounded-lg shadow-md p-8 max-w-2xl mx-auto">
            <div class="mb-6 text-green-500 flex justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
            </div>
            <h1 class="text-3xl font-bold mb-4">Thank You for Your Purchase!</h1>

            @if($type === 'test-pack')
                <p class="text-lg mb-6">Your order for <strong>{{ $product->Title }}</strong> has been successfully processed.</p>

                <div class="bg-muted/20 p-4 rounded-lg mb-6 text-left">
                    <h2 class="font-bold mb-2">Order Summary:</h2>
                    <div class="flex justify-between mb-2">
                        <span>Product:</span>
                        <span>{{ $product->Title }}</span>
                    </div>
                    @if($orderId)
                        <div class="flex justify-between mb-2">
                            <span>Order ID:</span>
                            <span id="order-id" data-order-id="{{ $orderId }}">{{ $orderId }}</span>
                        </div>
                    @endif
                    @if($email)
                        <div class="flex justify-between mb-2">
                            <span>Email:</span>
                            <span>{{ $email }}</span>
                        </div>
                    @endif
                    @if($product->hasActiveDiscount())
                        <div class="flex justify-between mb-2">
                            <span>Original Price:</span>
                            <span class="line-through text-gray-500">${{ number_format($product->Price, 2) }}</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span>Discount ({{ $product->getDiscountPercent() }}% OFF):</span>
                            <span class="text-green-600">-${{ number_format($product->getSavingsAmount(), 2) }}</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span>Discounted Price:</span>
                            <span>${{ number_format($product->getDiscountedPrice(), 2) }}</span>
                        </div>
                    @else
                        <div class="flex justify-between mb-2">
                            <span>Price:</span>
                            <span>${{ number_format($product->Price, 2) }}</span>
                        </div>
                    @endif
                    @if($total)
                        <div class="flex justify-between font-bold border-t pt-2">
                            <span>Total Paid:</span>
                            <span id="order-total" data-total="{{ $total }}">{{ $total }}</span>
                        </div>
                    @endif
                </div>

                <p class="mb-6">You will receive an email confirmation shortly with details about your purchase and how to access your content.</p>

                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('home') }}" class="bg-primary text-white px-6 py-3 rounded-md hover:bg-primary/90">
                        Return to Home
                    </a>
                    <a href="{{ route('dashboard.test-packs.index') }}" class="bg-secondary text-secondary-foreground px-6 py-3 rounded-md hover:bg-secondary/90">
                        View My Test Packs
                    </a>
                </div>

            @elseif($type === 'resource')
                <p class="text-lg mb-6">Your purchase of <strong>{{ $product['name'] }}</strong> has been successfully processed.</p>

                <div class="bg-muted/20 p-4 rounded-lg mb-6 text-left">
                    <h2 class="font-bold mb-2">Purchase Summary:</h2>
                    <div class="flex justify-between mb-2">
                        <span>Resource:</span>
                        <span>{{ $product['name'] }}</span>
                    </div>
                    @if($orderId)
                        <div class="flex justify-between mb-2">
                            <span>Order ID:</span>
                            <span>{{ $orderId }}</span>
                        </div>
                    @endif
                    @if($email)
                        <div class="flex justify-between mb-2">
                            <span>Email:</span>
                            <span>{{ $email }}</span>
                        </div>
                    @endif
                    @if($product['sale_price'] && $product['sale_price'] != $product['regular_price'])
                        <div class="flex justify-between mb-2">
                            <span>Original Price:</span>
                            <span class="line-through text-gray-500">${{ number_format($product['regular_price'], 2) }}</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span>Sale Price:</span>
                            <span class="text-green-600">${{ number_format($product['sale_price'], 2) }}</span>
                        </div>
                    @endif
                    @if($total)
                        <div class="flex justify-between font-bold border-t pt-2">
                            <span>Total Paid:</span>
                            <span>{{ $total }}</span>
                        </div>
                    @else
                        <div class="flex justify-between font-bold">
                            <span>Total:</span>
                            <span>${{ number_format($product['sale_price'] ?: $product['regular_price'], 2) }}</span>
                        </div>
                    @endif
                </div>

                <p class="mb-6">You now have lifetime access to this resource. You can download it anytime from your dashboard.</p>

                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('resources.show', $product['id']) }}"
                       class="bg-primary text-white px-6 py-3 rounded-md hover:bg-primary/90 text-center">
                        View Resource
                    </a>
                    <a href="{{ route('dashboard') }}"
                       class="bg-gray-100 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-200 text-center">
                        Go to Dashboard
                    </a>
                </div>
            @endif
        </div>
    </div>
</x-layouts.app>
