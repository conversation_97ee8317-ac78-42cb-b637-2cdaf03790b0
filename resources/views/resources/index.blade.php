<x-layouts.app>
    <x-slot name="title">Resources</x-slot>

    <!-- Header Section -->
    <section class="bg-gradient-to-br from-primary via-primary/90 to-primary/80 text-white py-16">
        <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-6">Shop Educational Resources</h1>
                <p class="text-xl text-white/90 max-w-3xl mx-auto">
                    Discover our collection of premium educational materials, study guides, and digital resources to support your learning journey.
                </p>
            </div>
        </div>
    </section>

    <!-- Resources Grid -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            @if(count($resources) > 0)
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($resources as $resource)
                        <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
                            <!-- Resource Image -->
                            <div class="aspect-w-16 aspect-h-9 bg-gray-200 overflow-hidden">
                                @if($resource['image'])
                                    <img
                                        src="{{ $resource['image'] }}"
                                        alt="{{ $resource['name'] }}"
                                        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                    >
                                @else
                                    <div class="w-full h-48 bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center">
                                        <svg class="w-16 h-16 text-primary/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                @endif
                            </div>

                            <!-- Resource Content -->
                            <div class="p-6">
                                <h3 class="text-lg font-semibold mb-2">
                                    <a href="{{ route('resources.show', $resource['id']) }}"
                                       class="text-gray-900 hover:text-primary transition-colors">
                                        {{ $resource['name'] }}
                                    </a>
                                </h3>

                                @if($resource['short_description'])
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                                        {!! strip_tags($resource['short_description']) !!}
                                    </p>
                                @endif

                                <!-- Price and Download Count -->
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex flex-col">
                                        @if($resource['sale_price'] && $resource['sale_price'] !== $resource['regular_price'])
                                            <span class="text-sm text-gray-500 line-through">${{ $resource['regular_price'] }}</span>
                                            <span class="text-lg font-bold text-primary">${{ $resource['sale_price'] }}</span>
                                        @else
                                            <span class="text-lg font-bold text-primary">
                                                @if($resource['price'] && $resource['price'] !== '0')
                                                    ${{ $resource['price'] }}
                                                @else
                                                    Free
                                                @endif
                                            </span>
                                        @endif
                                    </div>

                                    <div class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                        {{ $resource['download_count'] }} {{ Str::plural('file', $resource['download_count']) }}
                                    </div>
                                </div>

                                <!-- Action Button -->
                                <div class="mt-auto">
                                    <a
                                        href="{{ route('resources.show', $resource['id']) }}"
                                        class="block w-full bg-primary text-white text-center py-3 px-4 rounded-lg hover:bg-primary/90 transition-colors text-sm font-medium"
                                    >
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <!-- Empty State -->
                <div class="text-center py-12">
                    <svg class="mx-auto h-24 w-24 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-4 text-lg font-medium text-gray-900">No resources available</h3>
                    <p class="mt-2 text-gray-500">Check back later for new educational resources.</p>
                </div>
            @endif
        </div>
    </section>

    <!-- Call to Action -->
    <section class="bg-white py-16">
        <div class="container mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Looking for Test Packs?</h2>
            <p class="text-xl text-gray-600 mb-8">
                Explore our comprehensive test preparation materials designed to help students excel.
            </p>
            <a href="{{ route('test-packs.index') }}"
               class="inline-flex items-center px-6 py-3 bg-primary text-white font-medium rounded-lg hover:bg-primary/90 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                Shop Test Packs
            </a>
        </div>
    </section>
</x-layouts.app>
