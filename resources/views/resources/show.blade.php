<x-layouts.app>
    <x-slot name="title">{{ $product['name'] ?? 'Resource' }}</x-slot>

    @if($product)
        <!-- Resource Details -->
        <section class="py-16 bg-gray-50">
            <div class="container mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Resource Images Gallery -->
                        @php
                            $galleryImages = [];
                            if ($product['gallery'] && count($product['gallery']) > 0) {
                                foreach ($product['gallery'] as $index => $imageUrl) {
                                    $galleryImages[] = [
                                        'src' => $imageUrl,
                                        'alt' => $product['name'] . ' - Image ' . ($index + 1)
                                    ];
                                }
                            } elseif ($product['image']) {
                                $galleryImages[] = [
                                    'src' => $product['image'],
                                    'alt' => $product['name']
                                ];
                            }
                        @endphp

                        <x-lightbox-gallery
                            :images="$galleryImages"
                            :title="$product['name']"
                        />

                        <!-- Resource Information -->
                        <div class="p-8">
                            <div class="mb-6">
                                <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $product['name'] }}</h1>

                                <!-- Price -->
                                <div class="mb-6">
                                    @if($product['sale_price'] && $product['sale_price'] !== $product['regular_price'])
                                        <div class="flex items-center space-x-3">
                                            <span class="text-2xl text-gray-500 line-through">${{ $product['regular_price'] }}</span>
                                            <span class="text-3xl font-bold text-primary">${{ $product['sale_price'] }}</span>
                                            <span class="bg-red-100 text-red-600 px-2 py-1 rounded-full text-sm font-medium">
                                                Save ${{ number_format($product['regular_price'] - $product['sale_price'], 2) }}
                                            </span>
                                        </div>
                                    @else
                                        <span class="text-3xl font-bold text-primary">
                                            @if($product['price'] && $product['price'] !== '0')
                                                ${{ $product['price'] }}
                                            @else
                                                Free
                                            @endif
                                        </span>
                                    @endif
                                </div>

                                <!-- Resource Stats -->
                                <div class="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg mb-6">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-gray-900">{{ count($files) }}</div>
                                        <div class="text-sm text-gray-500">{{ Str::plural('File', count($files)) }}</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-gray-900">Digital</div>
                                        <div class="text-sm text-gray-500">Download</div>
                                    </div>
                                </div>

                                <!-- Purchase Button -->
                                @php
                                    $price = $product['sale_price'] ?: $product['price'];
                                @endphp

                                @if($price && $price > 0)
                                    <a href="{{ route('resources.checkout', $product['id']) }}"
                                       class="block w-full bg-primary text-white py-4 px-6 rounded-lg text-lg font-semibold hover:bg-primary/90 transition-colors mb-4 text-center">
                                        Purchase Resource - ${{ number_format($price, 2) }}
                                    </a>
                                @else
                                    <a href="{{ route('resources.claim', $product['id']) }}"
                                       class="block w-full bg-green-600 text-white py-4 px-6 rounded-lg text-lg font-semibold hover:bg-green-700 transition-colors mb-4 text-center">
                                        Download Free Resource
                                    </a>
                                @endif

                                <!-- Guarantee -->
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    30-day money back guarantee
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product Description -->
                @if($product['description'])
                    <div class="mt-12">
                        <div class="bg-white rounded-xl shadow-lg p-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">About This Resource</h2>
                            <div class="prose prose-gray max-w-none text-gray-600">
                                {!! $product['description'] !!}
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Back to Resources -->
                <div class="mt-8">
                    <a href="{{ route('resources.index') }}"
                       class="inline-flex items-center text-primary hover:text-primary/80 transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Resources
                    </a>
                </div>
            </div>
        </section>
    @else
        <!-- Error State -->
        <section class="py-16 bg-gray-50">
            <div class="container mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-3xl font-bold text-gray-900 mb-4">Resource Not Found</h1>
                <p class="text-gray-600 mb-8">The resource you're looking for doesn't exist or has been removed.</p>
                <a href="{{ route('resources.index') }}"
                   class="inline-flex items-center px-6 py-3 bg-primary text-white font-medium rounded-lg hover:bg-primary/90 transition-colors">
                    Browse All Resources
                </a>
            </div>
        </section>
    @endif
</x-layouts.app>
