<x-layouts.student-dashboard>
    <x-slot name="title">My Test Packs</x-slot>

    @if(count(Auth::guard('student')->user()->assignedTestPacks) > 0)
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach(Auth::guard('student')->user()->assignedTestPacks as $assignedPack)
                <div class="bg-white rounded-lg shadow overflow-hidden border border-gray-100">
                    <div class="p-5">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-lg font-semibold text-gray-900">{{ $assignedPack->testPack()->Title }}</h3>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Year {{ $assignedPack->testPack()->Level }}
                            </span>
                        </div>
                        <p class="text-sm text-gray-500 mb-4">{{ $assignedPack->testPack()->Category }}</p>
                        <div class="flex justify-end">
                            <a href="{{ config('services.naplan_exam_screen.url') }}/testpack/{{ $assignedPack->test_pack_id }}/{{ student()->ulid }}" class="inline-flex items-center px-4 py-2 bg-primary text-white text-sm font-medium rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                Start Test
                            </a>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <div class="bg-white rounded-lg shadow p-8 text-center">
            <div class="mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Test Packs Found</h3>
            <p class="text-gray-500 mb-6">You don't have any assigned test packs yet.</p>
            <p class="text-sm text-gray-500">Please contact your teacher or administrator to get access to test packs.</p>
        </div>
    @endif
</x-layouts.student-dashboard>
