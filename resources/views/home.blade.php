<x-layouts.app>
    <!-- Hero Section - White background (natural) -->
    <x-hero-section />

    <!-- Available Educational Packs - Parallax background -->
    <div class="relative overflow-hidden">
        <!-- Parallax Background -->
        <div class="absolute inset-0 bg-fixed bg-cover bg-center bg-no-repeat"
             style="background-image: url('https://images.pexels.com/photos/5212700/pexels-photo-5212700.jpeg?w=1920&auto=compress'); transform: translateZ(0);">
        </div>
        <!-- Overlay for better text readability -->
        <div class="absolute inset-0"></div>
        <!-- Content -->
        <div class="relative z-10 backdrop-blur-sm">
            <x-education-packs :testPacks="$testPacks" />
        </div>
    </div>

    <!-- Latest Blog Posts - Gray background -->
    <x-latest-blog-posts :posts="$latestPosts" class="bg-gray-50" />

    <!-- Features - White background -->
    <x-features-section class="bg-white" />

    <!-- About Section - Gray background -->
    <x-about-section class="bg-gray-50" />

    <!-- Interactive Question Types - White background -->
    <x-interactive-question-types class="bg-white" />

    <!-- Testimonials - Gray background -->
    <x-customer-reviews class="bg-gray-50" />

</x-layouts.app>
