<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('assigned_test_packs', function (Blueprint $table) {
            $table->foreignId('bought_test_pack_id')->nullable()->constrained('bought_test_packs');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assigned_test_packs', function (Blueprint $table) {
            $table->dropForeign(['bought_test_pack_id']);
            $table->dropColumn('bought_test_pack_id');
        });
    }
};
