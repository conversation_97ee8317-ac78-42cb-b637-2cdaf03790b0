<?php

namespace Database\Seeders;

use App\Models\User;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        /** @var User $user1 */
        $user1 = User::create([
            'name'     => 'Nguyen Viet',
            'email'    => '<EMAIL>',
            'password' => '$2y$12$waAoiiKwLZQzM5T2RWutWe0WVuM0D0uqDFmslSieG5blpx0Mq1qZy',
        ]);

        $user2 = User::create([
            'name'     => 'Admin',
            'email'    => '<EMAIL>',
            'password' => '123456'
        ]);

        $user1->boughtTestPacks()->create([
            'test_pack_id' => 1,
            'expires_at'   => now()->addYear()
        ]);

        foreach (range(1, 5) as $i) {
            $user1->students()->create([
                'name'     => 'Student ' . $i,
                'username' => 'student' . $i,
                'password' => '123456'
            ]);
        }
    }
}
