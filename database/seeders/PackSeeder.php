<?php

namespace Database\Seeders;

use App\Models\Pack;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;

class PackSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (App::isProduction()) {
            return;
        }

        $data = [
            [
                'name' => 'Year 3 NAPLAN* Online–style Full Test Pack',
                'grade' => 3,
                'category' => 'NAPLAN',
                'description' => 'This pack contains a full set of NAPLAN Online-style tests for Year 3 students. The tests are designed to be similar in format and content to the actual NAPLAN tests, allowing students to practice and prepare effectively.',
                'price' => 1900,
                'id_lms' => '776769',
            ],
            [
                'name' => 'Year 4 Opportunity Class Placement-style Test Pack',
                'grade' => 4,
                'category' => 'Opportunity Class',
                'description' => 'This pack contains comprehensive practice tests for Year 4 students preparing for the Opportunity Class Placement Test. Includes sections on Thinking Skills, Mathematical Reasoning, and Reading.',
                'price' => 5999,
                'id_lms' => '776772',
            ],
            [
                'name' => 'Year 5 NAPLAN* Online–style Full Test Pack',
                'grade' => 5,
                'category' => 'NAPLAN',
                'description' => 'This pack contains a full set of NAPLAN Online-style tests for Year 5 students. The tests are designed to be similar in format and content to the actual NAPLAN tests, allowing students to practice and prepare effectively.',
                'price' => 2900,
                'id_lms' => '776770',
            ],
            [
                'name' => 'Year 6 Selective School Test Preparation Pack',
                'grade' => 6,
                'category' => 'Selective',
                'description' => 'Complete preparation materials for Year 6 students aiming for selective high schools. Includes practice tests for all components: thinking skills, reading, mathematical reasoning and writing.',
                'price' => 7999,
                'id_lms' => '776773',
            ],
            [
                'name' => 'Year 7 NAPLAN* Online–style Full Test Pack',
                'grade' => 7,
                'category' => 'NAPLAN',
                'description' => 'This pack contains a full set of NAPLAN Online-style tests for Year 7 students. The tests are designed to be similar in format and content to the actual NAPLAN tests, allowing students to practice and prepare effectively.',
                'price' => 6900,
                'id_lms' => '776771',
            ],
        ];

        foreach ($data as $item) {
            Pack::create([
                'name' => $item['name'],
                'grade' => $item['grade'],
                'category' => $item['category'],
                'description' => $item['description'],
                'price' => $item['price'],
                'id_lms' => $item['id_lms'],
            ]);
        }
    }
}
