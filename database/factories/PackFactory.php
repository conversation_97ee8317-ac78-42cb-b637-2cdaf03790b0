<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Pack>
 */
class PackFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->word(),
            'description' => $this->faker->sentence(),
            'grade' => $this->faker->numberBetween(1, 12),
            'category' => $this->faker->word(),
            'price' => $this->faker->numberBetween(1000, 10000),
            'id_lms' => $this->faker->unique()->uuid(),
        ];
    }
}
