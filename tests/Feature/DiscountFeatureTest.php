<?php

use App\Models\User;
use App\Naplan\TestPack;
use App\Services\DiscountService;
use Carbon\Carbon;

describe('Discount Feature End-to-End Integration', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->testPack = new TestPack(
            Id: 1,
            Title: 'Integration Test Pack',
            Price: 49.99,
            Category: 'selective',
            Level: 7,
            Description: 'Test pack for integration testing',
            CreatedDate: Carbon::now()->toDateString(),
            TotalTests: 5,
            Tests: []
        );

        // Reset config
        config([
            'aussie.discount.percent' => null,
            'aussie.discount.start_date' => null,
            'aussie.discount.end_date' => null,
        ]);
    });

    describe('Environment Configuration Integration', function () {
        it('respects environment configuration for discount percentage', function () {
            // Simulate environment variables being loaded into config
            config([
                'aussie.discount.percent' => 25,
                'aussie.discount.start_date' => Carbon::now()->subDay()->toDateString(),
                'aussie.discount.end_date' => Carbon::now()->addDays(30)->toDateString(),
            ]);

            $discountService = app(DiscountService::class);

            expect($discountService->hasActiveDiscount())->toBeTrue();
            expect($discountService->getDiscountPercent())->toBe(25);
            expect($discountService->calculateDiscountedPrice(100))->toBe(75.0);
        });

        it('handles missing environment configuration gracefully', function () {
            // Simulate missing environment variables
            config([
                'aussie.discount.percent' => null,
                'aussie.discount.start_date' => null,
                'aussie.discount.end_date' => null,
            ]);

            $discountService = app(DiscountService::class);

            expect($discountService->hasActiveDiscount())->toBeFalse();
            expect($discountService->getDiscountPercent())->toBe(0);
            expect($discountService->calculateDiscountedPrice(100))->toBe(100.0);
        });
    });

    describe('UI Display Integration', function () {
        it('provides complete discount information for UI components', function () {
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => '2025-05-01',
                'aussie.discount.end_date' => '2025-06-30',
            ]);

            $discountService = app(DiscountService::class);
            $info = $discountService->getDiscountInfo();

            // This data would be used by Blade templates
            expect($info)->toMatchArray([
                'is_active' => true,
                'percent' => 20,
                'start_date' => '2025-05-01',
                'end_date' => '2025-06-30',
            ]);

            // TestPack should also provide consistent information
            expect($this->testPack->hasActiveDiscount())->toBeTrue();
            expect($this->testPack->getDiscountPercent())->toBe(20);
        });

        it('provides correct info when no discount is active for UI', function () {
            config([
                'aussie.discount.percent' => 0,
                'aussie.discount.start_date' => null,
                'aussie.discount.end_date' => null,
            ]);

            $discountService = app(DiscountService::class);
            $info = $discountService->getDiscountInfo();

            expect($info)->toMatchArray([
                'is_active' => false,
                'percent' => 0,
                'start_date' => null,
                'end_date' => null,
            ]);

            // TestPack should also reflect no discount
            expect($this->testPack->hasActiveDiscount())->toBeFalse();
            expect($this->testPack->getDiscountPercent())->toBe(0);
        });
    });

    describe('Checkout Integration Scenarios', function () {
        it('ensures checkout uses discounted pricing when discount is active', function () {
            config([
                'aussie.discount.percent' => 30,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            // Verify TestPack provides correct discounted price for checkout
            $originalPrice = $this->testPack->Price; // 49.99
            $discountedPrice = $this->testPack->getDiscountedPrice(); // Should be 34.99 (rounded)
            $expectedDiscountedPrice = round($originalPrice * 0.7, 2); // 30% discount, rounded

            expect($discountedPrice)->toBe($expectedDiscountedPrice);
            expect($this->testPack->getSavingsAmount())->toBe(round($originalPrice * 0.3, 2));

            // In real checkout, User::checkoutTestPack() would use getDiscountedPrice()
            // This ensures the integration works correctly
            expect($discountedPrice)->toBeLessThan($originalPrice);
        });

        it('ensures checkout uses original pricing when no discount is active', function () {
            config(['aussie.discount.percent' => 0]);

            $originalPrice = $this->testPack->Price;
            $discountedPrice = $this->testPack->getDiscountedPrice();

            expect($discountedPrice)->toBe($originalPrice);
            expect($this->testPack->getSavingsAmount())->toBe(0.0);
        });
    });

    describe('Marketing Campaign Scenarios', function () {
        it('handles flash sale scenario (very high discount, very short time)', function () {
            config([
                'aussie.discount.percent' => 80,
                'aussie.discount.start_date' => Carbon::now()->toDateString(),
                'aussie.discount.end_date' => Carbon::now()->toDateString(), // Same day
            ]);

            expect($this->testPack->hasActiveDiscount())->toBeTrue();
            expect($this->testPack->getDiscountPercent())->toBe(80);
            expect($this->testPack->getDiscountedPrice())->toBe(10.0); // 20% of 49.99 = 9.998, rounded to 10.00
            expect($this->testPack->getSavingsAmount())->toBe(39.99); // 80% of 49.99 = 39.992, rounded to 39.99
        });

        it('handles seasonal promotion (moderate discount, medium duration)', function () {
            config([
                'aussie.discount.percent' => 25,
                'aussie.discount.start_date' => Carbon::now()->subDays(7)->toDateString(),
                'aussie.discount.end_date' => Carbon::now()->addDays(23)->toDateString(), // 30 days total
            ]);

            expect($this->testPack->hasActiveDiscount())->toBeTrue();
            expect($this->testPack->getDiscountPercent())->toBe(25);
            expect($this->testPack->getDiscountedPrice())->toBe(37.49); // 75% of 49.99 = 37.4925, rounded to 37.49
            expect($this->testPack->getSavingsAmount())->toBe(12.5); // 25% of 49.99 = 12.4975, rounded to 12.50
        });

        it('handles loyalty program discount (small discount, no end date)', function () {
            config([
                'aussie.discount.percent' => 10,
                'aussie.discount.start_date' => Carbon::now()->subMonths(6)->toDateString(),
                'aussie.discount.end_date' => null, // Open-ended
            ]);

            expect($this->testPack->hasActiveDiscount())->toBeTrue();
            expect($this->testPack->getDiscountPercent())->toBe(10);
            expect($this->testPack->getDiscountedPrice())->toBe(44.99); // 90% of 49.99 = 44.991, rounded to 44.99
            expect($this->testPack->getSavingsAmount())->toBe(5.0); // 10% of 49.99 = 4.999, rounded to 5.00
        });
    });

    describe('Cross-Component Consistency', function () {
        it('ensures all components see the same discount state', function () {
            config([
                'aussie.discount.percent' => 15,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            // DiscountService (used directly)
            $discountService = app(DiscountService::class);

            // TestPack (uses DiscountService internally)
            $testPack = $this->testPack;

            // Both should report the same discount state
            expect($discountService->hasActiveDiscount())->toBe($testPack->hasActiveDiscount());
            expect($discountService->getDiscountPercent())->toBe($testPack->getDiscountPercent());

            // Price calculations should be consistent
            $serviceCalculatedPrice = $discountService->calculateDiscountedPrice($testPack->Price);
            expect($serviceCalculatedPrice)->toBe($testPack->getDiscountedPrice());

            $serviceCalculatedSavings = $discountService->calculateSavingsAmount($testPack->Price);
            expect($serviceCalculatedSavings)->toBe($testPack->getSavingsAmount());
        });
    });

    describe('System Robustness', function () {
        it('handles service container resolution consistently', function () {
            // Multiple resolutions should return the same instance (singleton)
            $service1 = app(DiscountService::class);
            $service2 = app(DiscountService::class);

            expect($service1)->toBe($service2);

            // Different TestPacks should use the same service instance
            $pack1 = new TestPack(1, 'Pack 1', 10.0, 'test', 1, 'Test', Carbon::now()->toDateString(), 1, []);
            $pack2 = new TestPack(2, 'Pack 2', 20.0, 'test', 2, 'Test', Carbon::now()->toDateString(), 2, []);

            config(['aussie.discount.percent' => 20]);

            // Both should see the same discount state
            expect($pack1->hasActiveDiscount())->toBe($pack2->hasActiveDiscount());
            expect($pack1->getDiscountPercent())->toBe($pack2->getDiscountPercent());
        });

        it('handles configuration changes gracefully', function () {
            // Start with no discount
            config(['aussie.discount.percent' => 0]);
            expect($this->testPack->hasActiveDiscount())->toBeFalse();

            // Add discount
            config([
                'aussie.discount.percent' => 25,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            // Should immediately reflect new configuration
            expect($this->testPack->hasActiveDiscount())->toBeTrue();
            expect($this->testPack->getDiscountPercent())->toBe(25);

            // Remove discount
            config(['aussie.discount.percent' => 0]);
            expect($this->testPack->hasActiveDiscount())->toBeFalse();
        });
    });
});
