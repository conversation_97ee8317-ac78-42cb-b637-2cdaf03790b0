<?php

use App\Services\DiscountService;
use App\Naplan\TestPack;
use Carbon\Carbon;

describe('DiscountService Feature Tests', function () {
    beforeEach(function () {
        // Reset config before each test
        config([
            'aussie.discount.percent' => null,
            'aussie.discount.start_date' => null,
            'aussie.discount.end_date' => null,
        ]);
    });

    describe('DiscountService Core Functionality', function () {
        it('can be resolved from service container', function () {
            $service = app(DiscountService::class);

            expect($service)->toBeInstanceOf(DiscountService::class);
        });

        it('returns same instance when resolved multiple times (singleton)', function () {
            $service1 = app(DiscountService::class);
            $service2 = app(DiscountService::class);

            expect($service1)->toBe($service2);
        });

        it('returns false when no discount is configured', function () {
            $discountService = app(DiscountService::class);

            expect($discountService->hasActiveDiscount())->toBeFalse();
            expect($discountService->getDiscountPercent())->toBe(0);
        });

        it('handles null discount configuration gracefully', function () {
            config(['aussie.discount.percent' => null]);

            $discountService = app(DiscountService::class);

            expect($discountService->hasActiveDiscount())->toBeFalse();
            expect($discountService->getDiscountPercent())->toBe(0);
            expect($discountService->calculateDiscountedPrice(100.00))->toBe(100.00);
        });

        it('handles zero discount percentage', function () {
            config(['aussie.discount.percent' => 0]);

            $discountService = app(DiscountService::class);

            expect($discountService->hasActiveDiscount())->toBeFalse();
            expect($discountService->getDiscountPercent())->toBe(0);
            expect($discountService->calculateDiscountedPrice(100.00))->toBe(100.00);
        });

        it('handles negative discount percentage', function () {
            config(['aussie.discount.percent' => -10]);

            $discountService = app(DiscountService::class);

            expect($discountService->hasActiveDiscount())->toBeFalse();
        });
    });

    describe('Date Range Validation', function () {
        it('returns false when current date is before start date', function () {
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => Carbon::tomorrow()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->addDays(7)->toDateString(),
            ]);

            $discountService = app(DiscountService::class);

            expect($discountService->hasActiveDiscount())->toBeFalse();
        });

        it('returns false when current date is after end date', function () {
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => Carbon::yesterday()->subDays(7)->toDateString(),
                'aussie.discount.end_date' => Carbon::yesterday()->toDateString(),
            ]);

            $discountService = app(DiscountService::class);

            expect($discountService->hasActiveDiscount())->toBeFalse();
        });

        it('returns true when current date is within date range', function () {
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $discountService = app(DiscountService::class);

            expect($discountService->hasActiveDiscount())->toBeTrue();
        });

        it('returns true when no date restrictions are set', function () {
            config([
                'aussie.discount.percent' => 15,
                'aussie.discount.start_date' => null,
                'aussie.discount.end_date' => null,
            ]);

            $discountService = app(DiscountService::class);

            expect($discountService->hasActiveDiscount())->toBeTrue();
        });

        it('handles start date boundary correctly', function () {
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => Carbon::today()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $discountService = app(DiscountService::class);

            expect($discountService->hasActiveDiscount())->toBeTrue();
        });

        it('handles end date boundary correctly', function () {
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::today()->toDateString(),
            ]);

            $discountService = app(DiscountService::class);

            expect($discountService->hasActiveDiscount())->toBeTrue();
        });
    });

    describe('Price Calculations', function () {
        it('calculates correct discounted price with 20% discount', function () {
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $discountService = app(DiscountService::class);

            expect($discountService->calculateDiscountedPrice(100.00))->toBe(80.00);
            expect($discountService->calculateSavingsAmount(100.00))->toBe(20.00);
        });

        it('calculates correct discounted price with 15% discount', function () {
            config([
                'aussie.discount.percent' => 15,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $discountService = app(DiscountService::class);

            expect($discountService->calculateDiscountedPrice(29.99))->toBe(25.49); // 85% of 29.99 = 25.4915, rounded to 25.49
            expect($discountService->calculateSavingsAmount(29.99))->toBe(4.5); // 15% of 29.99 = 4.4985, rounded to 4.50
        });

        it('handles zero price correctly', function () {
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $discountService = app(DiscountService::class);

            expect($discountService->calculateDiscountedPrice(0.00))->toBe(0.00);
            expect($discountService->calculateSavingsAmount(0.00))->toBe(0.00);
        });

        it('handles very high discount percentages', function () {
            config([
                'aussie.discount.percent' => 99,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $discountService = app(DiscountService::class);

            expect($discountService->calculateDiscountedPrice(100.00))->toBe(1.00);
            expect($discountService->calculateSavingsAmount(100.00))->toBe(99.00);
        });

        it('handles 100% discount correctly', function () {
            config([
                'aussie.discount.percent' => 100,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $discountService = app(DiscountService::class);

            expect($discountService->calculateDiscountedPrice(50.00))->toBe(0.00);
            expect($discountService->calculateSavingsAmount(50.00))->toBe(50.00);
        });
    });

    describe('Discount Information', function () {
        it('provides complete discount information when active', function () {
            config([
                'aussie.discount.percent' => 25,
                'aussie.discount.start_date' => '2025-05-01',
                'aussie.discount.end_date' => '2025-06-30',
            ]);

            $discountService = app(DiscountService::class);
            $info = $discountService->getDiscountInfo();

            expect($info)->toMatchArray([
                'is_active' => true,
                'percent' => 25,
                'start_date' => '2025-05-01',
                'end_date' => '2025-06-30',
            ]);
        });

        it('provides correct info when discount is inactive', function () {
            config([
                'aussie.discount.percent' => 0,
                'aussie.discount.start_date' => null,
                'aussie.discount.end_date' => null,
            ]);

            $discountService = app(DiscountService::class);
            $info = $discountService->getDiscountInfo();

            expect($info)->toMatchArray([
                'is_active' => false,
                'percent' => 0,
                'start_date' => null,
                'end_date' => null,
            ]);
        });
    });
});
