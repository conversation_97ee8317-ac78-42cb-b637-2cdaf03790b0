<?php

use App\Naplan\TestPack;
use App\Services\DiscountService;
use Carbon\Carbon;

describe('TestPack Discount Integration Feature Tests', function () {
    beforeEach(function () {
        // Reset config before each test
        config([
            'aussie.discount.percent' => null,
            'aussie.discount.start_date' => null,
            'aussie.discount.end_date' => null,
        ]);

        // Create a sample TestPack for testing
        $this->testPack = new TestPack(
            Id: 1,
            Title: 'Sample Test Pack',
            Price: 29.99,
            Category: 'naplanStyle',
            Level: 5,
            Description: 'Sample test pack for testing',
            CreatedDate: Carbon::now()->toDateString(),
            TotalTests: 3,
            Tests: []
        );
    });

    describe('TestPack Service Integration', function () {
        it('properly integrates with DiscountService through container', function () {
            config([
                'aussie.discount.percent' => 10,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            // These calls should work without throwing exceptions
            expect($this->testPack->hasActiveDiscount())->toBeTrue();
            expect($this->testPack->getDiscountPercent())->toBe(10);
            expect($this->testPack->getDiscountedPrice())->toBeLessThan($this->testPack->Price);
            expect($this->testPack->getSavingsAmount())->toBeGreaterThan(0);
        });

        it('delegates hasActiveDiscount to DiscountService correctly', function () {
            // No discount configured
            expect($this->testPack->hasActiveDiscount())->toBeFalse();

            // Configure discount
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            expect($this->testPack->hasActiveDiscount())->toBeTrue();
        });

        it('delegates getDiscountPercent to DiscountService correctly', function () {
            // No discount configured
            expect($this->testPack->getDiscountPercent())->toBe(0);

            // Configure 25% discount
            config([
                'aussie.discount.percent' => 25,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            expect($this->testPack->getDiscountPercent())->toBe(25);
        });

        it('delegates getDiscountedPrice to DiscountService with correct price', function () {
            // No discount - should return original price
            expect($this->testPack->getDiscountedPrice())->toBe(29.99);

            // 20% discount
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $expectedPrice = 29.99 * 0.8; // 20% discount = 23.992, rounded to 23.99
            expect($this->testPack->getDiscountedPrice())->toBe(23.99);
        });

        it('delegates getSavingsAmount to DiscountService correctly', function () {
            // No discount - should return 0
            expect($this->testPack->getSavingsAmount())->toBe(0.0);

            // 15% discount
            config([
                'aussie.discount.percent' => 15,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $expectedSavings = round(29.99 * 0.15, 2); // 15% of original price, rounded
            expect($this->testPack->getSavingsAmount())->toBe($expectedSavings);
        });
    });

    describe('Discount Consistency Validation', function () {
        it('ensures discounted price plus savings equals original price', function () {
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $originalPrice = $this->testPack->Price;
            $discountedPrice = $this->testPack->getDiscountedPrice();
            $savings = $this->testPack->getSavingsAmount();

            expect($discountedPrice + $savings)->toBe($originalPrice);
        });

        it('ensures savings calculation is consistent with discount percentage', function () {
            config([
                'aussie.discount.percent' => 25,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $originalPrice = $this->testPack->Price;
            $discountPercent = $this->testPack->getDiscountPercent();
            $savings = $this->testPack->getSavingsAmount();
            $expectedSavings = round($originalPrice * ($discountPercent / 100), 2);

            expect($savings)->toBe($expectedSavings);
        });
    });

    describe('Different TestPack Price Points', function () {
        it('applies discount correctly to expensive test packs', function () {
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $expensivePack = new TestPack(
                Id: 2,
                Title: 'Expensive Pack',
                Price: 99.99,
                Category: 'selective',
                Level: 8,
                Description: 'Expensive test pack',
                CreatedDate: Carbon::now()->toDateString(),
                TotalTests: 10,
                Tests: []
            );

            expect($expensivePack->hasActiveDiscount())->toBeTrue();
            expect($expensivePack->getDiscountPercent())->toBe(20);
            expect($expensivePack->getDiscountedPrice())->toBe(79.99); // 80% of 99.99 = 79.992, rounded to 79.99
            expect($expensivePack->getSavingsAmount())->toBe(20.0); // 20% of 99.99 = 19.998, rounded to 20.00
        });

        it('applies discount correctly to cheap test packs', function () {
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $cheapPack = new TestPack(
                Id: 3,
                Title: 'Cheap Pack',
                Price: 9.99,
                Category: 'naplanStyle',
                Level: 3,
                Description: 'Cheap test pack',
                CreatedDate: Carbon::now()->toDateString(),
                TotalTests: 1,
                Tests: []
            );

            expect($cheapPack->hasActiveDiscount())->toBeTrue();
            expect($cheapPack->getDiscountPercent())->toBe(20);
            expect($cheapPack->getDiscountedPrice())->toBe(7.99); // 80% of 9.99 = 7.992, rounded to 7.99
            expect($cheapPack->getSavingsAmount())->toBe(2.0); // 20% of 9.99 = 1.998, rounded to 2.00
        });

        it('handles free test packs correctly', function () {
            config([
                'aussie.discount.percent' => 25,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $freePack = new TestPack(
                Id: 1000,
                Title: 'Free Pack',
                Price: 0.0,
                Category: 'free',
                Level: 1,
                Description: 'Free pack',
                CreatedDate: Carbon::now()->toDateString(),
                TotalTests: 1,
                Tests: []
            );

            expect($freePack->hasActiveDiscount())->toBeTrue();
            expect($freePack->getDiscountPercent())->toBe(25);
            expect($freePack->getDiscountedPrice())->toBe(0.0);
            expect($freePack->getSavingsAmount())->toBe(0.0);
        });

        it('handles very small prices correctly', function () {
            config([
                'aussie.discount.percent' => 50,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $cheapPack = new TestPack(
                Id: 999,
                Title: 'Penny Pack',
                Price: 0.01,
                Category: 'test',
                Level: 1,
                Description: 'Very cheap pack',
                CreatedDate: Carbon::now()->toDateString(),
                TotalTests: 1,
                Tests: []
            );

            expect($cheapPack->hasActiveDiscount())->toBeTrue();
            expect($cheapPack->getDiscountPercent())->toBe(50);
            expect($cheapPack->getDiscountedPrice())->toBe(0.01); // 50% of 0.01 = 0.005, rounded to 0.01
            expect($cheapPack->getSavingsAmount())->toBe(0.0); // 0.01 - 0.01 = 0.00
        });
    });

    describe('Real World Discount Scenarios', function () {
        it('handles Black Friday scenario (high discount, limited time)', function () {
            config([
                'aussie.discount.percent' => 50,
                'aussie.discount.start_date' => Carbon::now()->toDateString(),
                'aussie.discount.end_date' => Carbon::now()->addDays(3)->toDateString(),
            ]);

            expect($this->testPack->hasActiveDiscount())->toBeTrue();
            expect($this->testPack->getDiscountPercent())->toBe(50);
            expect($this->testPack->getDiscountedPrice())->toBe(15.00);
            expect($this->testPack->getSavingsAmount())->toBe(14.99);
        });

        it('handles Early Bird discount (moderate discount, long period)', function () {
            config([
                'aussie.discount.percent' => 15,
                'aussie.discount.start_date' => Carbon::now()->subDays(30)->toDateString(),
                'aussie.discount.end_date' => Carbon::now()->addDays(60)->toDateString(),
            ]);

            expect($this->testPack->hasActiveDiscount())->toBeTrue();
            expect($this->testPack->getDiscountPercent())->toBe(15);
            expect($this->testPack->getDiscountedPrice())->toBe(25.49);
            expect($this->testPack->getSavingsAmount())->toBe(4.50);
        });

        it('handles expired promotion correctly', function () {
            config([
                'aussie.discount.percent' => 30,
                'aussie.discount.start_date' => Carbon::now()->subDays(10)->toDateString(),
                'aussie.discount.end_date' => Carbon::now()->subDay()->toDateString(),
            ]);

            expect($this->testPack->hasActiveDiscount())->toBeFalse();
            expect($this->testPack->getDiscountPercent())->toBe(0);
            expect($this->testPack->getDiscountedPrice())->toBe(29.99);
            expect($this->testPack->getSavingsAmount())->toBe(0.0);
        });

        it('handles future promotion correctly', function () {
            config([
                'aussie.discount.percent' => 20,
                'aussie.discount.start_date' => Carbon::now()->addDay()->toDateString(),
                'aussie.discount.end_date' => Carbon::now()->addDays(7)->toDateString(),
            ]);

            expect($this->testPack->hasActiveDiscount())->toBeFalse();
            expect($this->testPack->getDiscountPercent())->toBe(0);
            expect($this->testPack->getDiscountedPrice())->toBe(29.99);
            expect($this->testPack->getSavingsAmount())->toBe(0.0);
        });
    });

    describe('Edge Cases', function () {
        it('handles extremely high discount percentages', function () {
            config([
                'aussie.discount.percent' => 99,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            expect($this->testPack->getDiscountedPrice())->toBe(0.3); // 1% of 29.99 = 0.2999, rounded to 0.30
            expect($this->testPack->getSavingsAmount())->toBe(29.69); // 99% of 29.99 = 29.6901, rounded to 29.69
        });

        it('handles time zone boundaries correctly', function () {
            // Test that the discount properly handles start of day vs end of day
            $startDate = Carbon::now()->toDateString();
            $endDate = Carbon::now()->toDateString();

            config([
                'aussie.discount.percent' => 15,
                'aussie.discount.start_date' => $startDate,
                'aussie.discount.end_date' => $endDate,
            ]);

            // Should be active since we're within the same day
            expect($this->testPack->hasActiveDiscount())->toBeTrue();
        });
    });

    describe('Multiple TestPack Instances', function () {
        it('ensures all TestPack instances use the same DiscountService', function () {
            config([
                'aussie.discount.percent' => 30,
                'aussie.discount.start_date' => Carbon::yesterday()->toDateString(),
                'aussie.discount.end_date' => Carbon::tomorrow()->toDateString(),
            ]);

            $pack1 = new TestPack(
                Id: 1,
                Title: 'Pack 1',
                Price: 50.00,
                Category: 'test',
                Level: 5,
                Description: 'Test pack 1',
                CreatedDate: Carbon::now()->toDateString(),
                TotalTests: 1,
                Tests: []
            );

            $pack2 = new TestPack(
                Id: 2,
                Title: 'Pack 2',
                Price: 75.00,
                Category: 'test',
                Level: 6,
                Description: 'Test pack 2',
                CreatedDate: Carbon::now()->toDateString(),
                TotalTests: 2,
                Tests: []
            );

            // Both should have the same discount percentage
            expect($pack1->getDiscountPercent())->toBe(30);
            expect($pack2->getDiscountPercent())->toBe(30);

            // Both should have discount active
            expect($pack1->hasActiveDiscount())->toBeTrue();
            expect($pack2->hasActiveDiscount())->toBeTrue();

            // But different absolute prices and savings
            expect($pack1->getDiscountedPrice())->toBe(35.00);
            expect($pack2->getDiscountedPrice())->toBe(52.50);
            expect($pack1->getSavingsAmount())->toBe(15.00);
            expect($pack2->getSavingsAmount())->toBe(22.50);
        });
    });
});
