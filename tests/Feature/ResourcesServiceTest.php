<?php

namespace Tests\Feature;

use App\Services\ResourcesService;
use App\Services\Woocommerce;
use Tests\TestCase;
use Mockery;

class ResourcesServiceTest extends TestCase
{
    private $mockWoocommerce;
    private $resourcesService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockWoocommerce = Mockery::mock(Woocommerce::class);
        $this->resourcesService = new ResourcesService($this->mockWoocommerce);
    }

    public function test_can_get_all_digital_files()
    {
        // Mock the category lookup
        $this->mockWoocommerce
            ->shouldReceive('request')
            ->with('GET', 'products/categories', Mockery::any())
            ->once()
            ->andReturn([
                ['id' => 123, 'name' => 'Digital', 'slug' => 'digital']
            ]);

        // Mock the products request
        $mockProducts = [
            [
                'id' => 1,
                'name' => 'Test Digital Product',
                'slug' => 'test-digital-product',
                'price' => '29.99',
                'sale_price' => '',
                'regular_price' => '29.99',
                'short_description' => 'Test description',
                'downloadable' => true,
                'images' => [['src' => 'https://example.com/image.jpg']],
                'downloads' => [
                    [
                        'id' => 'download-1',
                        'name' => 'Test File.pdf',
                        'file' => 'https://example.com/test-file.pdf'
                    ]
                ],
                'date_created' => '2025-01-01T00:00:00',
                'date_modified' => '2025-01-01T00:00:00'
            ]
        ];

        $this->mockWoocommerce
            ->shouldReceive('request')
            ->with('GET', 'products', Mockery::any())
            ->once()
            ->andReturn($mockProducts);

        $result = $this->resourcesService->allFiles();

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertEquals('download-1', $result[0]['id']);
        $this->assertEquals('Test File.pdf', $result[0]['name']);
        $this->assertEquals('Test Digital Product', $result[0]['product_name']);
    }

    public function test_can_get_files_by_product_id()
    {
        $productId = 1;

        $this->mockWoocommerce
            ->shouldReceive('request')
            ->with('GET', "products/{$productId}")
            ->once()
            ->andReturn([
                'id' => $productId,
                'name' => 'Product with Files',
                'downloadable' => true,
                'downloads' => [
                    [
                        'id' => 'file-1',
                        'name' => 'File 1.pdf',
                        'file' => 'https://example.com/file1.pdf'
                    ]
                ],
                'price' => '29.99',
                'sale_price' => '',
                'regular_price' => '29.99',
                'short_description' => 'Product description',
                'images' => [],
                'slug' => 'product-with-files',
                'date_created' => '2025-01-01T00:00:00',
                'date_modified' => '2025-01-01T00:00:00'
            ]);

        $result = $this->resourcesService->getFilesByProductId($productId);

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertEquals($productId, $result[0]['product_id']);
    }

    public function test_returns_empty_array_when_product_not_downloadable()
    {
        $productId = 1;

        $this->mockWoocommerce
            ->shouldReceive('request')
            ->with('GET', "products/{$productId}")
            ->once()
            ->andReturn([
                'id' => $productId,
                'downloadable' => false, // Not downloadable
            ]);

        $result = $this->resourcesService->getFilesByProductId($productId);        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    public function test_handles_woocommerce_api_exception()
    {
        $this->mockWoocommerce
            ->shouldReceive('request')
            ->andThrow(new \Exception('API Error'));

        $result = $this->resourcesService->allFiles();

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
