# Resources Service

The `ResourcesService` provides a comprehensive interface to interact with digital resources from WooCommerce. This service automatically fetches digital/downloadable products from the "digital" category and exposes their downloadable files as resources.

## Configuration

Make sure you have the following environment variables set in your `.env` file:

```env
WOO_API_KEY=your_woocommerce_api_key
WOO_API_SECRET=your_woocommerce_api_secret
WOO_API_URL=https://your-woocommerce-site.com
```

## Usage

### Basic Usage

```php
use App\Services\DigitalFilesService;

// Get the service instance (automatically injected via DI)
$digitalFilesService = app(DigitalFilesService::class);

// Get all digital files
$files = $digitalFilesService->allFiles();

// Get files with pagination
$files = $digitalFilesService->allFiles(page: 1, perPage: 20);
```

### Available Methods

#### `allFiles(int $page = 1, int $perPage = 100): array`
Get all digital files from WooCommerce with pagination support.

```php
$files = $digitalFilesService->allFiles(1, 50);
```

#### `getFilesByProductId(int $productId): array`
Get all digital files for a specific product.

```php
$files = $digitalFilesService->getFilesByProductId(123);
```

#### `getFileById(int $productId, string $downloadId): ?array`
Get a specific digital file by product ID and download ID.

```php
$file = $digitalFilesService->getFileById(123, 'download-abc-123');
```

#### `getDigitalProducts(int $page = 1, int $perPage = 100): array`
Get digital products (without detailed file information).

```php
$products = $digitalFilesService->getDigitalProducts(1, 25);
```

#### `getTotalFilesCount(): int`
Get the total count of all digital files.

```php
$totalFiles = $digitalFilesService->getTotalFilesCount();
```

#### `getTotalProductsCount(): int`
Get the total count of digital products.

```php
$totalProducts = $digitalFilesService->getTotalProductsCount();
```

#### `fileExists(int $productId, string $downloadId): bool`
Check if a specific file exists.

```php
$exists = $digitalFilesService->fileExists(123, 'download-abc-123');
```

#### `getDigitalCategories(): array`
Get all categories that contain digital products.

```php
$categories = $digitalFilesService->getDigitalCategories();
```

## Data Structure

### File Array Structure
Each file returned by the service has the following structure:

```php
[
    'id' => 'download-id',                    // Download ID from WooCommerce
    'name' => 'File Name.pdf',                // File display name
    'file' => 'https://example.com/file.pdf', // Download URL
    'product_id' => 123,                      // Associated product ID
    'product_name' => 'Product Name',         // Product title
    'product_slug' => 'product-slug',         // Product slug
    'product_price' => '29.99',               // Current price
    'product_sale_price' => '19.99',          // Sale price (if on sale)
    'product_regular_price' => '29.99',       // Regular price
    'product_description' => 'Description',   // Product short description
    'product_image' => 'https://...',         // Product featured image
    'created_at' => '2025-01-01T00:00:00',   // Product creation date
    'modified_at' => '2025-01-01T00:00:00',  // Product modification date
]
```

### Product Array Structure
When fetching products, each product has this structure:

```php
[
    'id' => 123,
    'name' => 'Product Name',
    'slug' => 'product-slug',
    'price' => '29.99',
    'sale_price' => '19.99',
    'regular_price' => '29.99',
    'description' => 'Full description',
    'short_description' => 'Short description',
    'image' => 'https://example.com/image.jpg',
    'gallery' => ['https://...', 'https://...'],
    'download_count' => 3,
    'downloads' => [/* array of download objects */],
    'created_at' => '2025-01-01T00:00:00',
    'modified_at' => '2025-01-01T00:00:00',
    'status' => 'publish',
    'featured' => false,
]
```

## API Endpoints

If you include the routes file, the following endpoints will be available:

- `GET /digital-files` - Get all files with pagination
- `GET /digital-files/products` - Get digital products
- `GET /digital-files/categories` - Get digital categories
- `GET /digital-files/stats` - Get statistics
- `GET /digital-files/products/{id}/files` - Get files for a product
- `GET /digital-files/products/{id}/files/{downloadId}` - Get specific file
- `GET /digital-files/products/{id}/files/{downloadId}/exists` - Check if file exists

## Requirements

- WooCommerce API v3
- Products must be marked as "downloadable"
- Products should be in the "digital" category (configurable)
- Products must have type "simple"
- Products must be published

## Error Handling

The service includes comprehensive error handling:

- API failures return empty arrays/null instead of throwing exceptions
- All errors are logged to Laravel's log system
- Failed requests don't break the application flow

## Testing

Run the included tests:

```bash
php artisan test tests/Feature/DigitalFilesServiceTest.php
```

## Customization

You can customize the digital category by modifying the `$digitalCategoryName` property in the service:

```php
protected $digitalCategoryName = 'your-custom-category';
```
