<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="300" fill="#87CEEB"/>

  <!-- Animated Clouds -->
  <g class="clouds">
    <!-- Cloud 1 -->
    <g transform="translate(50, 30)">
      <animateTransform
        attributeName="transform"
        type="translate"
        values="50,30; 420,30; 50,30"
        dur="20s"
        repeatCount="indefinite"/>
      <ellipse cx="0" cy="0" rx="25" ry="15" fill="white" opacity="0.8"/>
      <ellipse cx="20" cy="-5" rx="30" ry="20" fill="white" opacity="0.8"/>
      <ellipse cx="40" cy="0" rx="25" ry="15" fill="white" opacity="0.8"/>
      <ellipse cx="20" cy="10" rx="20" ry="12" fill="white" opacity="0.8"/>
    </g>

    <!-- Cloud 2 -->
    <g transform="translate(200, 50)">
      <animateTransform
        attributeName="transform"
        type="translate"
        values="200,50; 520,50; 200,50"
        dur="25s"
        repeatCount="indefinite"/>
      <ellipse cx="0" cy="0" rx="20" ry="12" fill="white" opacity="0.7"/>
      <ellipse cx="15" cy="-3" rx="25" ry="16" fill="white" opacity="0.7"/>
      <ellipse cx="30" cy="0" rx="20" ry="12" fill="white" opacity="0.7"/>
      <ellipse cx="15" cy="8" rx="15" ry="10" fill="white" opacity="0.7"/>
    </g>

    <!-- Cloud 3 (smaller, faster) -->
    <g transform="translate(300, 20)">
      <animateTransform
        attributeName="transform"
        type="translate"
        values="300,20; 450,20; 300,20"
        dur="15s"
        repeatCount="indefinite"/>
      <ellipse cx="0" cy="0" rx="15" ry="9" fill="white" opacity="0.6"/>
      <ellipse cx="12" cy="-2" rx="18" ry="12" fill="white" opacity="0.6"/>
      <ellipse cx="24" cy="0" rx="15" ry="9" fill="white" opacity="0.6"/>
    </g>
  </g>

  <!-- Ground -->
  <rect x="0" y="200" width="400" height="100" fill="#90EE90"/>

  <!-- Study Scene -->
  <!-- Desk -->
  <rect x="150" y="180" width="100" height="20" fill="#8B4513" rx="5"/>

  <!-- Books -->
  <rect x="160" y="160" width="15" height="20" fill="#FF6B6B"/>
  <rect x="175" y="155" width="15" height="25" fill="#4ECDC4"/>
  <rect x="190" y="158" width="15" height="22" fill="#45B7D1"/>

  <!-- Laptop -->
  <rect x="210" y="170" width="25" height="15" fill="#2C3E50" rx="2"/>
  <rect x="212" y="172" width="21" height="11" fill="#3498DB"/>

  <!-- Person studying -->
  <circle cx="200" cy="140" r="12" fill="#FDBCB4"/> <!-- Head -->
  <rect x="195" y="150" width="10" height="15" fill="#E74C3C"/> <!-- Body -->
  <rect x="192" y="165" width="6" height="8" fill="#3498DB"/> <!-- Legs -->
  <rect x="202" y="165" width="6" height="8" fill="#3498DB"/>

  <!-- Sun -->
  <circle cx="350" cy="50" r="25" fill="#FFD700">
    <animate attributeName="opacity" values="0.8;1;0.8" dur="3s" repeatCount="indefinite"/>
  </circle>

  <!-- Sun rays -->
  <g stroke="#FFD700" stroke-width="2" stroke-linecap="round" opacity="0.7">
    <line x1="350" y1="15" x2="350" y2="25">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite"/>
    </line>
    <line x1="385" y1="50" x2="375" y2="50">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite" begin="0.5s"/>
    </line>
    <line x1="350" y1="85" x2="350" y2="75">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite" begin="1s"/>
    </line>
    <line x1="315" y1="50" x2="325" y2="50">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite" begin="1.5s"/>
    </line>
  </g>

  <!-- Floating study elements -->
  <g class="floating-elements">
    <!-- Mathematical symbols -->
    <text x="50" y="100" font-family="Arial" font-size="16" fill="#666" opacity="0.6">∑</text>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="0,0; 0,-10; 0,0"
      dur="4s"
      repeatCount="indefinite"/>

    <text x="320" y="120" font-family="Arial" font-size="14" fill="#666" opacity="0.6">π</text>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="0,0; 0,-8; 0,0"
      dur="3.5s"
      repeatCount="indefinite"
      begin="1s"/>
  </g>
</svg>
