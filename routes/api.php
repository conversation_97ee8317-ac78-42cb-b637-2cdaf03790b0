<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\StudentAuthController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::post('/students/login', [StudentAuthController::class, 'validateCredentials']);
Route::post('/login', [AuthController::class, 'validateCredentials']);

// Resources API routes
require __DIR__ . '/resources.php';


