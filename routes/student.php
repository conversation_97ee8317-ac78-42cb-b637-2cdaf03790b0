<?php

use App\Http\Controllers\Student\StudentAuthController;
use App\Http\Controllers\Student\StudentDashboardController;
use App\Http\Controllers\Student\StudentSettingsController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Student Authentication Routes
|--------------------------------------------------------------------------
*/

Route::prefix('student')->name('student.')->group(function () {
    // Guest routes (login)
    Route::middleware('guest')->group(function () {
        Route::get('/login', [StudentAuthController::class, 'create'])->name('login');
        Route::post('/login', [StudentAuthController::class, 'store']);
    });

    // Authenticated student routes
    Route::middleware('auth:student')->group(function () {
        Route::get('/', [StudentDashboardController::class, 'index'])->name('dashboard');
        Route::get('/test-packs', [StudentDashboardController::class, 'testPacks'])->name('test-packs');
        Route::get('/settings', [StudentSettingsController::class, 'index'])->name('settings');
    });

    // Logout (no auth middleware as it should work even if session expires)
    Route::post('/logout', [StudentAuthController::class, 'destroy'])->name('logout');
});
