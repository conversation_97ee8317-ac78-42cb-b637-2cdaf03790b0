<?php

use App\Http\Controllers\ResourcesController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Resources API Routes
|--------------------------------------------------------------------------
|
| These routes handle resources from WooCommerce
|
*/

Route::prefix('resources')->group(function () {
        // Get all resources with pagination
    Route::get('/', [ResourcesController::class, 'index']);

    // Get resource products
    Route::get('/products', [ResourcesController::class, 'products']);

    // Get resource categories
    Route::get('/categories', [ResourcesController::class, 'categories']);

    // Get dashboard stats
    Route::get('/stats', [ResourcesController::class, 'stats']);

    // Get files for a specific product
    Route::get('/products/{productId}/files', [ResourcesController::class, 'productFiles']);

    // Get a specific file
    Route::get('/products/{productId}/files/{downloadId}', [ResourcesController::class, 'show']);

    // Check if a file exists
    Route::get('/products/{productId}/files/{downloadId}/exists', [ResourcesController::class, 'fileExists']);
});
