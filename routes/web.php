<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\ResourcesPageController;
use App\Http\Controllers\TestPackController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\CheckoutSuccessController;
use Illuminate\Support\Facades\Route;

// Home page
Route::get('/', HomeController::class)->name('home');

// Unified checkout success
Route::get('/checkout/success', [CheckoutSuccessController::class, 'success'])->name('checkout.success');

// Test Packs
Route::prefix('test-packs')->group(function () {
    Route::get('/', [TestPackController::class, 'index'])->name('test-packs.index');
    Route::get('/{id}', [TestPackController::class, 'show'])->name('testPacks.show');
    Route::get('/{id}/checkout', [TestPackController::class, 'checkout'])
        ->middleware('auth')
        ->name('testPacks.checkout');
    Route::get('/{id}/claim', [TestPackController::class, 'claim'])
        ->middleware('auth')
        ->name('testPacks.claim');
});

// Static pages
Route::name('pages.')->group(function () {
    Route::get('/about', [PageController::class, 'about'])->name('about');
    Route::get('/contact', [PageController::class, 'contact'])->name('contact');
    Route::get('/privacy-policy', [PageController::class, 'privacy'])->name('privacy');
    Route::get('/terms-and-conditions', [PageController::class, 'terms'])->name('terms');
    Route::get('/faq', [PageController::class, 'faq'])->name('faq');
});

// Blog routes
Route::prefix('blog')->name('blog.')->group(function () {
    Route::get('/', [BlogController::class, 'index'])->name('index');
    Route::get('/{slug}', [BlogController::class, 'show'])->name('show');
});

Route::prefix('resources')->name('resources.')->group(function () {
    Route::get('/', [ResourcesPageController::class, 'index'])->name('index');
    Route::get('/{id}', [ResourcesPageController::class, 'show'])->name('show');
    Route::get('/{id}/checkout', [ResourcesPageController::class, 'checkout'])
        ->middleware('auth')
        ->name('checkout');
    Route::get('/{id}/claim', [ResourcesPageController::class, 'claim'])
        ->middleware('auth')
        ->name('claim');
});


// Parent dashboard routes
require __DIR__ . '/dashboard.php';

// Student routes
require __DIR__ . '/student.php';

// Dummy email test route
Route::get('/test-email', function () {
    try {
        \Illuminate\Support\Facades\Mail::raw('This is a test email from your Laravel application!', function ($message) {
            $message->to('<EMAIL>')
                ->subject('Test Email from ' . config('app.name'));
        });

        return response()->json([
            'success' => true,
            'message' => 'Test email sent <NAME_EMAIL>'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to send email: ' . $e->getMessage()
        ], 500);
    }
})->name('test-email');

// Preview resource purchase email
Route::get('/preview-resource-email', function () {
    // Create mock data for preview
    $mockUser = new \App\Models\User([
        'name' => 'John Doe',
        'email' => '<EMAIL>'
    ]);

    $mockBoughtResource = new \App\Models\BoughtResource([
        'user_id' => 1,
        'resource_id' => '123'
    ]);
    $mockBoughtResource->user = $mockUser;

    $resourcesService = app(\App\Services\ResourcesService::class);

    // Return the mailable instance for proper preview
    return new \App\Mail\ResourcePurchased($mockBoughtResource, $resourcesService);
})->name('preview-resource-email');


