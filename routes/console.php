<?php

use App\Actions\AssignTestPackToUser;
use App\Models\Student;
use App\Models\User;
use App\Naplan\TestPackRepository;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

Artisan::command('dau', function () {
    $student = Student::where('username', 'student1')->first();
    $student->delete();
});

Artisan::command('temp:setup', function () {
    $testPacks = app(TestPackRepository::class)->allTestPacks();
    $user = User::where('email', '<EMAIL>')->firstOrFail();

    foreach ($testPacks as $testPack) {
        $this->info('Assigning test pack: ' . $testPack->Title);
        AssignTestPackToUser::make()->handle($testPack->Id, $user);
    }
});
