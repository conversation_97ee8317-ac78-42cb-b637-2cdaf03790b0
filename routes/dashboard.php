<?php

use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\ConfirmablePasswordController;
use App\Http\Controllers\Auth\EmailVerificationNotificationController;
use App\Http\Controllers\Auth\EmailVerificationPromptController;
use App\Http\Controllers\Auth\NewPasswordController;
use App\Http\Controllers\Auth\PasswordResetLinkController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Dashboard\TestPackController as DashboardTestPackController;
use App\Http\Controllers\Dashboard\StudentController as DashboardStudentController;
use App\Http\Controllers\Dashboard\ResourceController as DashboardResourceController;
use App\Http\Controllers\Dashboard\SettingsController;
use Illuminate\Auth\Middleware\EnsureEmailIsVerified;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Authentication Routes
|--------------------------------------------------------------------------
*/

Route::middleware('guest')->group(function () {
    // Login
    Route::get('login', [AuthenticatedSessionController::class, 'create'])->name('login');
    Route::post('login', [AuthenticatedSessionController::class, 'store']);

    // Registration
    Route::get('register', [RegisteredUserController::class, 'create'])->name('register');
    Route::post('register', [RegisteredUserController::class, 'store']);

    // Password Reset
    Route::get('forgot-password', [PasswordResetLinkController::class, 'create'])->name('password.request');
    Route::post('forgot-password', [PasswordResetLinkController::class, 'store'])->name('password.email');
    Route::get('reset-password/{token}', [NewPasswordController::class, 'create'])->name('password.reset');
    Route::post('reset-password', [NewPasswordController::class, 'store'])->name('password.store');
});

/*
|--------------------------------------------------------------------------
| Dashboard Routes
|--------------------------------------------------------------------------
*/

/*
|--------------------------------------------------------------------------
| Email Verification Routes (must be accessible to authenticated users)
|--------------------------------------------------------------------------
*/

Route::middleware('auth')->group(function () {
    // Email Verification
    Route::prefix('email')->group(function () {
        Route::get('verify', EmailVerificationPromptController::class)
            ->name('verification.notice');
        Route::get('verify/{id}/{hash}', VerifyEmailController::class)
            ->middleware(['signed', 'throttle:6,1'])
            ->name('verification.verify');
        Route::post('verification-notification', [EmailVerificationNotificationController::class, 'store'])
            ->middleware('throttle:6,1')
            ->name('verification.send');
    });

    // Password Confirmation
    Route::get('confirm-password', [ConfirmablePasswordController::class, 'show'])
        ->name('password.confirm');
    Route::post('confirm-password', [ConfirmablePasswordController::class, 'store']);

    // Logout
    Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])
        ->name('logout');
});

/*
|--------------------------------------------------------------------------
| Protected Dashboard Routes (require email verification)
|--------------------------------------------------------------------------
*/

Route::middleware(['auth', EnsureEmailIsVerified::class])->group(function () {
    // Main Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Dashboard Features
    Route::prefix('dashboard')->name('dashboard.')->group(function () {
        // Test Packs Management
        Route::get('/test-packs', [DashboardTestPackController::class, 'index'])->name('test-packs.index');
        Route::get('/test-packs/{boughtTestPack}', [DashboardTestPackController::class, 'show'])->name('test-packs.show');

        // Resources Management
        Route::get('/resources', [DashboardResourceController::class, 'index'])->name('resources.index');
        Route::get('/resources/{boughtResource}', [DashboardResourceController::class, 'show'])->name('resources.show');

        // Students Management
        Route::get('/students', [DashboardStudentController::class, 'index'])->name('students.index');

        // Settings
        Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
    });
});
